import pandas as pd
import numpy as np
import os
import glob
import ast
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

def load_population_patterns(file_path):
    """
    加载群体层面模式数据，提取最高频组合模式
    """
    print(f"\n=== Loading Population Patterns ===")
    print(f"File: {file_path}")
    
    try:
        df = pd.read_excel(file_path)
        
        # 检查必要的列
        required_cols = ['population', 'phase_id', 'rank', 'pattern_combination']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"  Error: Missing columns {missing_cols}")
            return None
        
        print(f"  Data shape: {df.shape}")
        print(f"  Populations: {sorted(df['population'].unique())}")
        print(f"  Phases: {sorted(df['phase_id'].unique())}")
        
        return df
        
    except Exception as e:
        print(f"  Error loading population patterns: {e}")
        return None

def extract_mainstream_patterns(population_df):
    """
    提取每个细分阶段下不同人群的最高频组合模式 (rank=1)
    """
    print(f"\n=== Extracting Mainstream Patterns ===")
    
    # 只保留rank=1的最高频模式
    top_patterns = population_df[population_df['rank'] == 1].copy()
    
    mainstream_patterns = {}
    
    for _, row in top_patterns.iterrows():
        population = row['population']
        phase_id = row['phase_id']
        pattern = row['pattern_combination']
        
        if phase_id not in mainstream_patterns:
            mainstream_patterns[phase_id] = {}
        
        mainstream_patterns[phase_id][population] = pattern
        
        print(f"  Phase {phase_id}, Population {population}: {pattern}")
    
    # 显示每个阶段的模式分布
    print(f"\n  Mainstream patterns summary:")
    for phase_id in sorted(mainstream_patterns.keys()):
        patterns = mainstream_patterns[phase_id]
        print(f"    Phase {phase_id}:")
        for pop in sorted(patterns.keys()):
            print(f"      Population {pop}: {patterns[pop]}")
        
        # 检查是否有相同的模式
        pattern_values = list(patterns.values())
        unique_patterns = list(set(pattern_values))
        if len(unique_patterns) < len(pattern_values):
            print(f"      Note: Some populations share the same pattern")
    
    return mainstream_patterns

def load_step_level_patterns(file_path):
    """
    加载步级模式数据
    """
    print(f"\n=== Loading Step Level Patterns ===")
    print(f"File: {file_path}")
    
    try:
        df = pd.read_excel(file_path)
        
        # 检查必要的列
        required_cols = ['step_cycle_id', 'phase_id', 'population', 'element_pattern']
        missing_cols = [col for col in required_cols if col not in df.columns]
        
        if missing_cols:
            print(f"  Error: Missing columns {missing_cols}")
            return None
        
        print(f"  Data shape: {df.shape}")
        print(f"  Populations: {sorted(df['population'].unique())}")
        print(f"  Phases: {sorted(df['phase_id'].unique())}")
        
        # 只保留PD患者数据 (population = 0, 1)
        pd_data = df[df['population'].isin([0, 1])].copy()
        print(f"  PD patient data: {len(pd_data)} records")
        print(f"  PD populations: {sorted(pd_data['population'].unique())}")
        
        return pd_data
        
    except Exception as e:
        print(f"  Error loading step level patterns: {e}")
        return None

def parse_step_cycle_id(step_cycle_id):
    """
    解析step_cycle_id，提取组成部分
    例子: "0_Ding Guoming_Ding Guoming Cal 03_L_gait_phase_cycle_177"
    """
    try:
        parts = step_cycle_id.split('_')
        
        # 提取population (第一个部分)
        population_prefix = parts[0]
        
        # 提取步编号 (最后一个gait_phase_cycle_xxx部分)
        gait_cycle_parts = []
        for i, part in enumerate(parts):
            if part == 'gait' and i + 2 < len(parts) and parts[i+1] == 'phase' and parts[i+2] == 'cycle':
                if i + 3 < len(parts):
                    gait_cycle_number = parts[i+3]
                    gait_cycle_id = f"gait_phase_cycle_{gait_cycle_number}"
                    break
        else:
            gait_cycle_id = None
        
        # 提取被试名 (population_prefix之后到Cal之前的部分)
        remaining = '_'.join(parts[1:])
        if ' Cal ' in remaining:
            subject_part = remaining.split(' Cal ')[0]
        else:
            subject_part = None
        
        return {
            'population_prefix': population_prefix,
            'subject_name': subject_part,
            'gait_cycle_id': gait_cycle_id,
            'gait_cycle_number': gait_cycle_number if 'gait_cycle_number' in locals() else None
        }
        
    except Exception as e:
        print(f"Error parsing step_cycle_id: {step_cycle_id}, {e}")
        return None

def load_phase_data_files(base_dir):
    """
    加载所有阶段的Excel文件
    """
    print(f"\n=== Loading Phase Data Files ===")
    print(f"Base directory: {base_dir}")
    
    phase_data = {}
    
    # 查找所有L_*.xlsx文件
    pattern = os.path.join(base_dir, "L_*.xlsx")
    files = glob.glob(pattern)
    
    for file_path in sorted(files):
        filename = os.path.basename(file_path)
        phase_id = filename.replace('.xlsx', '')  # L_0, L_1, etc.
        
        try:
            df = pd.read_excel(file_path)
            
            # 检查必要的列
            required_cols = ['File_ID']
            emg_cols = [f'EMG{i}' for i in range(1, 9)]
            emg_norm_cols = [f'EMG{i}_norm' for i in range(1, 9)]
            
            if 'Cluster_Label' in df.columns:
                required_cols.append('Cluster_Label')
            
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                print(f"  Warning: {filename} missing columns: {missing_cols}")
                continue
            
            phase_data[phase_id] = df
            print(f"  Loaded {filename}: {len(df)} records")
            
        except Exception as e:
            print(f"  Error loading {filename}: {e}")
    
    return phase_data

def find_matching_features(step_patterns_df, phase_data, mainstream_patterns):
    """
    根据步编号在阶段数据中找到对应的特征信息
    """
    print(f"\n=== Finding Matching Features ===")
    
    matching_results = []
    
    for _, row in step_patterns_df.iterrows():
        step_cycle_id = row['step_cycle_id']
        phase_id = row['phase_id']
        population = row['population']
        element_pattern = row['element_pattern']
        
        # 解析step_cycle_id
        parsed_info = parse_step_cycle_id(step_cycle_id)
        if parsed_info is None or parsed_info['gait_cycle_id'] is None:
            continue
        
        gait_cycle_id = parsed_info['gait_cycle_id']
        
        # 检查该阶段是否有对应的数据文件
        if phase_id not in phase_data:
            continue
        
        phase_df = phase_data[phase_id]
        
        # 在File_ID中查找包含gait_cycle_id的记录
        matching_records = phase_df[phase_df['File_ID'].str.contains(gait_cycle_id, na=False)]
        
        if len(matching_records) > 0:
            # 添加解析信息和匹配结果
            for _, match_row in matching_records.iterrows():
                result = {
                    'step_cycle_id': step_cycle_id,
                    'phase_id': phase_id,
                    'population': population,
                    'element_pattern': element_pattern,
                    'gait_cycle_id': gait_cycle_id,
                    'subject_name': parsed_info['subject_name'],
                    'matched_file_id': match_row['File_ID']
                }
                
                # 添加EMG特征
                emg_cols = [f'EMG{i}' for i in range(1, 9)]
                emg_norm_cols = [f'EMG{i}_norm' for i in range(1, 9)]
                
                for col in emg_cols + emg_norm_cols:
                    if col in match_row:
                        result[col] = match_row[col]
                
                if 'Cluster_Label' in match_row:
                    result['cluster_label'] = match_row['Cluster_Label']
                
                matching_results.append(result)
    
    matching_df = pd.DataFrame(matching_results)
    
    if len(matching_df) > 0:
        print(f"  Found {len(matching_df)} matching feature records")
        print(f"  Unique step cycles: {matching_df['step_cycle_id'].nunique()}")
        print(f"  Phases: {sorted(matching_df['phase_id'].unique())}")
        print(f"  Populations: {sorted(matching_df['population'].unique())}")
    else:
        print(f"  No matching features found")
    
    return matching_df

def calculate_mainstream_pattern_ratios(step_patterns_df, mainstream_patterns):
    """
    计算PD患者中各主流组合模式的出现比率
    """
    print(f"\n=== Calculating Mainstream Pattern Ratios ===")
    
    # 解析element_pattern字符串
    def parse_pattern(pattern_str):
        try:
            if isinstance(pattern_str, str):
                pattern = ast.literal_eval(pattern_str)
                if isinstance(pattern, (list, tuple)):
                    return tuple(sorted(pattern))
            return None
        except:
            return None
    
    step_patterns_df['parsed_element_pattern'] = step_patterns_df['element_pattern'].apply(parse_pattern)
    
    # 过滤掉无法解析的模式
    valid_data = step_patterns_df[step_patterns_df['parsed_element_pattern'].notna()].copy()
    
    print(f"  Valid patterns: {len(valid_data)} / {len(step_patterns_df)}")
    
    ratio_results = []
    
    # 按阶段分析
    for phase_id in sorted(mainstream_patterns.keys()):
        print(f"\n  Analyzing Phase {phase_id}:")
        
        phase_data = valid_data[valid_data['phase_id'] == phase_id]
        
        if len(phase_data) == 0:
            print(f"    No data for phase {phase_id}")
            continue
        
        # 获取该阶段的主流模式
        phase_mainstream = mainstream_patterns[phase_id]
        
        # 解析主流模式
        mainstream_parsed = {}
        for pop, pattern_str in phase_mainstream.items():
            parsed = parse_pattern(pattern_str)
            if parsed is not None:
                mainstream_parsed[pop] = parsed
        
        print(f"    Mainstream patterns: {mainstream_parsed}")
        
        # 收集所有主流模式（去重）
        all_mainstream_patterns = list(set(mainstream_parsed.values()))
        
        print(f"    Unique mainstream patterns: {all_mainstream_patterns}")
        
        # 按PD人群分析
        for pd_population in [0, 1]:
            pd_data = phase_data[phase_data['population'] == pd_population]
            
            if len(pd_data) == 0:
                print(f"    No data for PD population {pd_population}")
                continue
            
            total_steps = len(pd_data)
            print(f"    PD Population {pd_population}: {total_steps} steps")
            
            # 计算每个主流模式的出现次数
            for i, mainstream_pattern in enumerate(all_mainstream_patterns):
                # 找到使用这个主流模式的原始人群
                source_populations = [pop for pop, pattern in mainstream_parsed.items() 
                                    if pattern == mainstream_pattern]
                
                # 计算该模式在当前PD人群中的出现次数
                pattern_count = (pd_data['parsed_element_pattern'] == mainstream_pattern).sum()
                ratio = pattern_count / total_steps if total_steps > 0 else 0.0
                
                result = {
                    'phase_id': phase_id,
                    'pd_population': pd_population,
                    'mainstream_pattern_index': i + 1,
                    'mainstream_pattern': str(mainstream_pattern),
                    'source_populations': str(source_populations),
                    'pattern_count': pattern_count,
                    'total_steps': total_steps,
                    'ratio': ratio
                }
                
                ratio_results.append(result)
                
                print(f"      Pattern {i+1} {mainstream_pattern}: {pattern_count}/{total_steps} = {ratio:.4f}")
                print(f"        (Originally from populations: {source_populations})")
    
    ratio_df = pd.DataFrame(ratio_results)
    
    print(f"\n  Ratio calculation complete: {len(ratio_df)} results")
    
    return ratio_df

def create_summary_statistics(ratio_df):
    """
    创建汇总统计
    """
    print(f"\n=== Creating Summary Statistics ===")
    
    # 1. 按阶段汇总
    phase_summary = ratio_df.groupby(['phase_id', 'mainstream_pattern_index']).agg({
        'pd_population': 'count',
        'ratio': ['mean', 'std', 'min', 'max']
    }).round(4)
    
    # 2. 按PD人群汇总
    population_summary = ratio_df.groupby(['pd_population', 'mainstream_pattern_index']).agg({
        'phase_id': 'count',
        'ratio': ['mean', 'std', 'min', 'max']
    }).round(4)
    
    # 3. 整体统计
    overall_summary = ratio_df.groupby('mainstream_pattern_index').agg({
        'ratio': ['count', 'mean', 'std', 'min', 'max']
    }).round(4)
    
    return phase_summary, population_summary, overall_summary

def save_results(matching_features_df, ratio_df, summaries, mainstream_patterns, output_dir):
    """
    保存所有结果
    """
    print(f"\n=== Saving Results ===")
    
    # 1. 主流模式信息
    mainstream_list = []
    for phase_id, patterns in mainstream_patterns.items():
        for population, pattern in patterns.items():
            mainstream_list.append({
                'phase_id': phase_id,
                'population': population,
                'mainstream_pattern': pattern
            })
    
    mainstream_df = pd.DataFrame(mainstream_list)
    mainstream_file = os.path.join(output_dir, 'mainstream_patterns.xlsx')
    mainstream_df.to_excel(mainstream_file, index=False)
    print(f"  Saved mainstream patterns: {mainstream_file}")
    
    # 2. 匹配的特征数据
    if len(matching_features_df) > 0:
        features_file = os.path.join(output_dir, 'matched_features.xlsx')
        matching_features_df.to_excel(features_file, index=False)
        print(f"  Saved matched features: {features_file}")
    
    # 3. 比率分析结果
    ratio_file = os.path.join(output_dir, 'mainstream_pattern_ratios.xlsx')
    ratio_df.to_excel(ratio_file, index=False)
    print(f"  Saved pattern ratios: {ratio_file}")
    
    # 4. 汇总统计
    phase_summary, population_summary, overall_summary = summaries
    
    with pd.ExcelWriter(os.path.join(output_dir, 'ratio_statistics.xlsx')) as writer:
        phase_summary.to_excel(writer, sheet_name='Phase_Summary')
        population_summary.to_excel(writer, sheet_name='Population_Summary')
        overall_summary.to_excel(writer, sheet_name='Overall_Summary')
    
    print(f"  Saved statistics: ratio_statistics.xlsx")

def create_detailed_report(mainstream_patterns, ratio_df, summaries, output_dir):
    """
    创建详细分析报告
    """
    phase_summary, population_summary, overall_summary = summaries
    
    report_path = os.path.join(output_dir, 'mainstream_pattern_analysis_report.txt')
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("=== Mainstream Pattern Analysis Report ===\n\n")
        
        f.write("1. MAINSTREAM PATTERNS IDENTIFIED\n")
        f.write("-" * 50 + "\n")
        for phase_id in sorted(mainstream_patterns.keys()):
            f.write(f"Phase {phase_id}:\n")
            patterns = mainstream_patterns[phase_id]
            for pop in sorted(patterns.keys()):
                f.write(f"  Population {pop}: {patterns[pop]}\n")
            f.write("\n")
        
        f.write("2. PD PATIENT PATTERN USAGE RATIOS\n")
        f.write("-" * 50 + "\n")
        for phase_id in sorted(ratio_df['phase_id'].unique()):
            f.write(f"Phase {phase_id}:\n")
            phase_data = ratio_df[ratio_df['phase_id'] == phase_id]
            
            for pd_pop in sorted(phase_data['pd_population'].unique()):
                f.write(f"  PD Population {pd_pop}:\n")
                pop_data = phase_data[phase_data['pd_population'] == pd_pop]
                
                for _, row in pop_data.iterrows():
                    f.write(f"    Pattern {row['mainstream_pattern_index']}: {row['ratio']:.4f} "
                           f"({row['pattern_count']}/{row['total_steps']})\n")
                    f.write(f"      Pattern: {row['mainstream_pattern']}\n")
                    f.write(f"      Source: {row['source_populations']}\n")
                f.write("\n")
        
        f.write("3. SUMMARY STATISTICS\n")
        f.write("-" * 50 + "\n")
        f.write("Overall pattern usage statistics:\n")
        for pattern_idx, row in overall_summary.iterrows():
            f.write(f"  Pattern {pattern_idx}: mean={row[('ratio', 'mean')]:.4f}, "
                   f"std={row[('ratio', 'std')]:.4f}, count={row[('ratio', 'count')]}\n")
        f.write("\n")
        
        f.write("4. METHODOLOGY\n")
        f.write("-" * 50 + "\n")
        f.write("1. Extracted mainstream patterns (rank=1) from population analysis\n")
        f.write("2. Matched step-level patterns with phase data using gait cycle IDs\n")
        f.write("3. Calculated usage ratios for each mainstream pattern in PD populations\n")
        f.write("4. Maintained separate categories even when patterns are identical\n\n")
        
        f.write("5. OUTPUT FILES\n")
        f.write("-" * 50 + "\n")
        f.write("- mainstream_patterns.xlsx: Identified mainstream patterns\n")
        f.write("- matched_features.xlsx: Matched EMG features\n")
        f.write("- mainstream_pattern_ratios.xlsx: Usage ratios in PD patients\n")
        f.write("- ratio_statistics.xlsx: Statistical summaries\n")
    
    print(f"  Saved detailed report: {report_path}")

def main():
    """
    主函数 - 主流模式分析
    """
    # 路径设置
    BASE_DIR = r"C:\YOU_task\synergy\synergy_analysis_pahse_results_vaf\phase_separated_K_optional\L_final_results_together"
    ENHANCED_DIR = os.path.join(BASE_DIR, "synergy_pattern_combination_analysis_enhanced")
    
    POPULATION_FILE = os.path.join(ENHANCED_DIR, "population_level_patterns_with_ci.xlsx")
    STEP_FILE = os.path.join(ENHANCED_DIR, "step_level_patterns.xlsx")
    OUTPUT_DIR = os.path.join(BASE_DIR, "mainstream_pattern_analysis")
    
    print("=== Mainstream Pattern Analysis ===")
    print(f"Population patterns file: {POPULATION_FILE}")
    print(f"Step patterns file: {STEP_FILE}")
    print(f"Phase data directory: {BASE_DIR}")
    print(f"Output directory: {OUTPUT_DIR}")
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 1. 加载群体层面模式数据
    population_df = load_population_patterns(POPULATION_FILE)
    if population_df is None:
        return
    
    # 2. 提取主流组合模式
    mainstream_patterns = extract_mainstream_patterns(population_df)
    
    # 3. 加载步级模式数据
    step_patterns_df = load_step_level_patterns(STEP_FILE)
    if step_patterns_df is None:
        return
    
    # 4. 加载阶段数据文件
    phase_data = load_phase_data_files(BASE_DIR)
    
    # 5. 找到匹配的特征数据
    matching_features_df = find_matching_features(step_patterns_df, phase_data, mainstream_patterns)
    
    # 6. 计算PD患者中主流模式的出现比率
    ratio_df = calculate_mainstream_pattern_ratios(step_patterns_df, mainstream_patterns)
    
    # 7. 创建汇总统计
    summaries = create_summary_statistics(ratio_df)
    
    # 8. 保存结果
    save_results(matching_features_df, ratio_df, summaries, mainstream_patterns, OUTPUT_DIR)
    
    # 9. 创建详细报告
    create_detailed_report(mainstream_patterns, ratio_df, summaries, OUTPUT_DIR)
    
    # 10. 打印最终摘要
    print(f"\n{'='*80}")
    print(f"MAINSTREAM PATTERN ANALYSIS COMPLETE")
    print(f"{'='*80}")
    print(f"Mainstream patterns identified: {len([p for patterns in mainstream_patterns.values() for p in patterns])}")
    print(f"PD patient steps analyzed: {step_patterns_df['step_cycle_id'].nunique()}")
    print(f"Ratio calculations: {len(ratio_df)}")
    print(f"Matched features: {len(matching_features_df)}")
    print(f"Results saved to: {OUTPUT_DIR}")

if __name__ == "__main__":
    main()