import numpy as np
import pandas as pd
import os
import matplotlib.pyplot as plt
import matplotlib.markers as mmarkers
from tqdm import tqdm
import scipy.signal as signal
from scipy.interpolate import interp1d

"""     # 参考Vicon csv文件中的列名
    list_name_Trajectories = ['Frame', 'Sub Frame',
                 'LASI.X', 'LASI.Y', 'LASI.Z', # LASI -Left Anterior Superior Iliac （左髂前上髂）
                 'RASI.X', 'RASI.Y', 'RASI.Z', # RASI -Right Anterior Superior Iliac （右髂前上髂）
                 'LPSI.X', 'LPSI.Y', 'LPSI.Z', # LPSI -Left Posterior Superior Iliac （左髂后上髂）
                 'RPSI.X', 'RPSI.Y', 'RPSI.Z', # RPSI -Right Posterior Superior Iliac （右髂后上髂）
                 'LTHI.X', 'LTHI.Y', 'LTHI.Z', # LTHI -Left The thigh Markers （左大腿）
                 'LKAX.X', 'LKAX.Y', 'LKAX.Z', # LKAX -Left Knee Axis （左膝关节轴）(NULL)
                 'LKD1.X', 'LKD1.Y', 'LKD1.Z', # LKD1 -Left Knee Device 1  （左膝关节驱动器1）(NULL)
                 'LKD2.X', 'LKD2.Y', 'LKD2.Z', # LKD2 -Left Knee Device 2  （左膝关节驱动器2）(NULL)
                 'LKNE.X', 'LKNE.Y', 'LKNE.Z', # LKNE -Left Knee Markers （左膝关节）
                 'LTIB.X', 'LTIB.Y', 'LTIB.Z', # LTIB -Left Tibia Markers （左胫骨，小腿）
                 'LANK.X', 'LANK.Y', 'LANK.Z', # LANK -Left Ankle Markers （左踝关节）
                 'LHEE.X', 'LHEE.Y', 'LHEE.Z', # LHEE -Left Heel Markers （左脚跟）
                 'LTOE.X', 'LTOE.Y', 'LTOE.Z', # LTOE -Left Toe Markers （左脚趾）
                 'RTHI.X', 'RTHI.Y', 'RTHI.Z', # RTHI -Right The thigh Markers （右大腿）
                 'RKAX.X', 'RKAX.Y', 'RKAX.Z', # LKAX -right Knee Axis （右膝关节轴）(NULL)
                 'RKD1.X', 'RKD1.Y', 'RKD1.Z', # RKD1 -Right Knee Device 1  （右膝关节驱动器1）(NULL)
                 'RKD2.X', 'RKD2.Y', 'RKD2.Z', # RKD2 -Right Knee Device 2  （右膝关节驱动器2）(NULL)
                 'RKNE.X', 'RKNE.Y', 'RKNE.Z', # RKNE -Right Knee Markers （右膝关节）
                 'RTIB.X', 'RTIB.Y', 'RTIB.Z', # RTIB -Right Tibia Markers （右胫骨）
                 'RANK.X', 'RANK.Y', 'RANK.Z', # RANK -Right Ankle Markers （右踝关节）
                 'RHEE.X', 'RHEE.Y', 'RHEE.Z', # RHEE -Right Heel Markers （右脚跟）
                 'RTOE.X', 'RTOE.Y', 'RTOE.Z', # RTOE -Right Toe Markers （右脚趾）
                 'LMED.X', 'LMED.Y', 'LMED.Z', # LMED -Left Medial Meniscus （左内踝标志物）
                 'RMED.X', 'RMED.Y', 'RMED.Z', # RMED -Right Medial Meniscus （右内踝标志物）
                 ]
 """

# 定义函数获得字典中关键字对应的数组最长的长度
def get_max_len(dict_data):
    max_len = 0
    for key in dict_data.keys():
        if len(dict_data[key]) > max_len:
            max_len = len(dict_data[key])
    return max_len

# 定义函数将字典中关键字对应的数组补齐到相同长度
def fill_dict_data(dict_data):
    max_len = get_max_len(dict_data)
    for key in dict_data.keys():
        if len(dict_data[key]) < max_len:
            # 补齐到相同长度,缺少的值用None填充
            dict_data[key] = np.concatenate((dict_data[key], [None] * (max_len - len(dict_data[key]))), axis=0)
    return dict_data

def get_angle(point_side1_1, point_side1_2, point_side1_3, point_side2_1, point_side2_2, point_side2_3):
    side_1_12 = np.linalg.norm(point_side1_1 - point_side1_2)
    side_1_23 = np.linalg.norm(point_side1_2 - point_side1_3)
    side_2_12 = np.linalg.norm(point_side2_1 - point_side2_2)
    side_2_23 = np.linalg.norm(point_side2_2 - point_side2_3)

    angel_1 = np.arccos(np.dot(point_side1_2 - point_side1_1, point_side1_2 - point_side1_3) / (side_1_12 * side_1_23))
    angel_2 = np.arccos(np.dot(point_side2_2 - point_side2_1, point_side2_2 - point_side2_3) / (side_2_12 * side_2_23))

    # # 转化弧度制为角度制
    # angel_1 = angel_1 * 180 / np.pi
    # angel_2 = angel_2 * 180 / np.pi

    return angel_1, angel_2

def get_knee_angle(vicon_data):
    RTHI = vicon_data[[f"RTHI.{axis}" for axis in "XYZ"]].to_numpy()
    RKNE = vicon_data[[f"RKNE.{axis}" for axis in "XYZ"]].to_numpy()
    RTIB = vicon_data[[f"RTIB.{axis}" for axis in "XYZ"]].to_numpy()
    LTHI = vicon_data[[f"LTHI.{axis}" for axis in "XYZ"]].to_numpy()
    LKNE = vicon_data[[f"LKNE.{axis}" for axis in "XYZ"]].to_numpy()
    LTIB = vicon_data[[f"LTIB.{axis}" for axis in "XYZ"]].to_numpy()

    # 定义数组存储角度
    RKNEE_angle = np.zeros(RTHI.shape[0])
    LKNEE_angle = np.zeros(RTHI.shape[0])

    # 计算膝关节夹角 (三维角度)
    for i in range(RTHI.shape[0]):
        RKNEE_angle_i, LKNEE_angle_i = get_angle(RTHI[i], RKNE[i], RTIB[i], LTHI[i], LKNE[i], LTIB[i])
        # 存入数组
        RKNEE_angle[i] = RKNEE_angle_i
        LKNEE_angle[i] = LKNEE_angle_i

    return RKNEE_angle, LKNEE_angle

def get_ankle_angle(vicon_data):
    RTIB = vicon_data[[f"RTIB.{axis}" for axis in "XYZ"]].to_numpy()
    RANK = vicon_data[[f"RANK.{axis}" for axis in "XYZ"]].to_numpy()
    RTOE = vicon_data[[f"RTOE.{axis}" for axis in "XYZ"]].to_numpy()
    LTIB = vicon_data[[f"LTIB.{axis}" for axis in "XYZ"]].to_numpy()
    LANK = vicon_data[[f"LANK.{axis}" for axis in "XYZ"]].to_numpy()
    LTOE = vicon_data[[f"LTOE.{axis}" for axis in "XYZ"]].to_numpy()

    # 定义数组存储角度
    RANK_angle = np.zeros(RTIB.shape[0])
    LANK_angle = np.zeros(RTIB.shape[0])

    # 计算踝关节夹角 (三维角度)
    for i in range(RTIB.shape[0]):
        RANK_angle_i, LANK_angle_i = get_angle(RTIB[i], RANK[i], RTOE[i], LTIB[i], LANK[i], LTOE[i])
        # 存入数组
        RANK_angle[i] = RANK_angle_i
        LANK_angle[i] = LANK_angle_i

    return RANK_angle, LANK_angle

def get_joint_angle_speed(angle_data, frame = 100):
    angle_diff = np.diff(angle_data)
    angle_speed = angle_diff * (frame)
    return angle_speed

def central_difference(data, fs):
    """
    中心差分：
    Compute velocity and acceleration using central differences.
    :param data: 1D array of angle data
    :param fs: Sampling frequency in Hz
    :return: (velocity, acceleration)
    """
    dt = 1 / fs
    vel = np.zeros_like(data)
    acc = np.zeros_like(data)
    
    # Central difference for interior points
    vel[1:-1] = (data[2:] - data[:-2]) / (2 * dt)
    acc[1:-1] = (data[2:] - 2*data[1:-1] + data[:-2]) / (dt**2)
    
    # Forward/backward difference for endpoints
    vel[0] = (data[1] - data[0]) / dt
    vel[-1] = (data[-1] - data[-2]) / dt
    acc[0] = acc[1]
    acc[-1] = acc[-2]
    
    return vel, acc

def butterworth_zero_phase(data, fs, cutoff=6, order=4):
    """
    Apply a zero-phase (forward-backward) Butterworth low-pass filter.
    :param data: 1D array of raw angle data (degrees or radians)
    :param fs: Sampling frequency in Hz
    :param cutoff: Cutoff frequency in Hz
    :param order: Filter order
    :return: Filtered data array
    """
    # 检查原始数据中是否缺值 有Nan值 若是在中间则根据插值补上 若是在最后则删除该行
    if np.any(np.isnan(data)):
        data = np.interp(np.arange(len(data)), np.arange(len(data))[~np.isnan(data)], data[~np.isnan(data)])

    nyq = 0.5 * fs
    norm_cutoff = cutoff / nyq
    b, a = signal.butter(order, norm_cutoff, btype='low', analog=False)

    
    return signal.filtfilt(b, a, data)


# 定义三类人群
groups = ["healthy", "PD_early", "PD_mid"]

# 获取vicon数据路径 - 修改为第四章数据路径
vicon_path = 'D:/论文/中期/第四章/数据/VICON/vicon_raw/'
output_dir = 'D:/论文/中期/第四章/数据/VICON/vicon_joint/'

# 创建输出目录
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("开始处理三类人群的VICON数据...")
print(f"数据路径: {vicon_path}")
print(f"输出路径: {output_dir}")
print(f"处理人群: {groups}")

# 处理三类人群数据
for group in groups:
    print(f"\n正在处理人群: {group}")
    group_path = os.path.join(vicon_path, group)
    
    if not os.path.exists(group_path):
        print(f"警告: 人群路径 {group_path} 不存在!")
        continue
    
    # 获取该组下的所有受试者文件夹
    subject_folders = [f for f in os.listdir(group_path) if os.path.isdir(os.path.join(group_path, f))]
    print(f"找到 {len(subject_folders)} 个受试者: {subject_folders}")
    
    # 读取该组下的所有受试者文件夹
    for subject_folder in tqdm(subject_folders, desc=f"处理 {group} 组"):
        subject_path = os.path.join(group_path, subject_folder)
        
        # 获取该受试者的所有CSV文件
        csv_files = [f for f in os.listdir(subject_path) if f.endswith('.csv')]
        print(f"  受试者 {subject_folder}: 找到 {len(csv_files)} 个CSV文件")
        
        # 处理该受试者的所有CSV文件
        for csv_file in csv_files:
            csv_path = os.path.join(subject_path, csv_file)
            # 提取文件名中的ID（去掉_vicon.csv后缀）
            vicon_name_temp = csv_file.replace('_vicon.csv', '').replace('.csv', '')
            
            try:
                seg_key_dict = {}
                fs = 100  # 采样率 100 Hz

                # 读取vicon数据
                temp_vicon_data = pd.read_csv(csv_path)
                
                # 检查必要的列是否存在
                required_cols = ['RTHI.X', 'RTHI.Y', 'RTHI.Z', 'RKNE.X', 'RKNE.Y', 'RKNE.Z',
                               'RTIB.X', 'RTIB.Y', 'RTIB.Z', 'LTHI.X', 'LTHI.Y', 'LTHI.Z',
                               'LKNE.X', 'LKNE.Y', 'LKNE.Z', 'LTIB.X', 'LTIB.Y', 'LTIB.Z',
                               'RANK.X', 'RANK.Y', 'RANK.Z', 'RTOE.X', 'RTOE.Y', 'RTOE.Z',
                               'LANK.X', 'LANK.Y', 'LANK.Z', 'LTOE.X', 'LTOE.Y', 'LTOE.Z']
                
                missing_cols = [col for col in required_cols if col not in temp_vicon_data.columns]
                if missing_cols:
                    print(f"    警告: 文件 {csv_file} 缺少列: {missing_cols}")
                    continue

                # 计算膝关节和踝关节角度
                RKNEE_angle, LKNEE_angle = get_knee_angle(temp_vicon_data)
                RANK_angle, LANK_angle = get_ankle_angle(temp_vicon_data)

                # 对角度进行滤波
                RKNEE_angle = butterworth_zero_phase(RKNEE_angle, fs, cutoff=6, order=4)
                LKNEE_angle = butterworth_zero_phase(LKNEE_angle, fs, cutoff=6, order=4)
                RANK_angle = butterworth_zero_phase(RANK_angle, fs, cutoff=6, order=4)
                LANK_angle = butterworth_zero_phase(LANK_angle, fs, cutoff=6, order=4)

                # 计算关节角速度
                RKNEE_angle_speed = get_joint_angle_speed(RKNEE_angle)
                LKNEE_angle_speed = get_joint_angle_speed(LKNEE_angle)
                RANK_angle_speed = get_joint_angle_speed(RANK_angle)
                LANK_angle_speed = get_joint_angle_speed(LANK_angle)

                # 计算关节角加速度
                RKNEE_angle_acc = get_joint_angle_speed(RKNEE_angle_speed)
                LKNEE_angle_acc = get_joint_angle_speed(LKNEE_angle_speed)
                RANK_angle_acc = get_joint_angle_speed(RANK_angle_speed)
                LANK_angle_acc = get_joint_angle_speed(LANK_angle_speed)
                
                # 保存数据
                # 去除"Frame"和"Sub Frame"中的空值，保存原始数据中的"Frame"和"Sub Frame"列
                temp_vicon_data = temp_vicon_data.dropna(subset=['Frame', 'Sub Frame'])
                temp_vicon_data = temp_vicon_data.reset_index(drop=True)
                seg_key_dict['Frame'] = temp_vicon_data['Frame']
                seg_key_dict['Sub Frame'] = temp_vicon_data['Sub Frame']

                # 添加组别和受试者信息
                seg_key_dict['Group'] = [group] * len(temp_vicon_data)
                seg_key_dict['Subject_ID'] = [subject_folder] * len(temp_vicon_data)
                seg_key_dict['File_Name'] = [vicon_name_temp] * len(temp_vicon_data)

                # 关节角度数据
                seg_key_dict['RKNEE_angle'] = RKNEE_angle
                seg_key_dict['LKNEE_angle'] = LKNEE_angle
                seg_key_dict['RANK_angle'] = RANK_angle
                seg_key_dict['LANK_angle'] = LANK_angle

                # 关节角速度数据
                seg_key_dict['RKNEE_angle_speed'] = RKNEE_angle_speed
                seg_key_dict['LKNEE_angle_speed'] = LKNEE_angle_speed
                seg_key_dict['RANK_angle_speed'] = RANK_angle_speed
                seg_key_dict['LANK_angle_speed'] = LANK_angle_speed

                # 关节角加速度数据
                seg_key_dict['RKNEE_angle_acc'] = RKNEE_angle_acc
                seg_key_dict['LKNEE_angle_acc'] = LKNEE_angle_acc
                seg_key_dict['RANK_angle_acc'] = RANK_angle_acc
                seg_key_dict['LANK_angle_acc'] = LANK_angle_acc

                # 创建输出目录结构
                temp_output_dir = os.path.join(output_dir, group)
                if not os.path.exists(temp_output_dir):
                    os.makedirs(temp_output_dir)
                
                # 输出文件路径
                output_path = os.path.join(temp_output_dir, f"{subject_folder}_{vicon_name_temp}_joint.csv")
                
                # 输出的是相对该文件下的关键帧 从0开始的参考
                seg_key_dict = fill_dict_data(seg_key_dict)
                pd.DataFrame(seg_key_dict).to_csv(output_path, index=False)
                
                print(f"    处理完成: {subject_folder}/{csv_file} -> {os.path.basename(output_path)}")
                
            except Exception as e:
                print(f"    错误: 处理文件 {csv_file} 时出错: {str(e)}")
                continue

print('\n所有数据处理完成!')
print(f"结果保存在: {output_dir}")

# 统计处理结果
total_files = 0
for group in groups:
    group_output_path = os.path.join(output_dir, group)
    if os.path.exists(group_output_path):
        group_files = len([f for f in os.listdir(group_output_path) if f.endswith('.csv')])
        print(f"{group} 组: 处理了 {group_files} 个文件")
        total_files += group_files

print(f"总共处理了 {total_files} 个文件")
