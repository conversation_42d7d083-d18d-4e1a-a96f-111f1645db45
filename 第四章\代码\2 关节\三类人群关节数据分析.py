import pandas as pd
import numpy as np
import os
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def calculate_trial_statistics(data_df):
    """计算单次测试的统计特征"""
    features = ['RKNEE_angle', 'LKNEE_angle', 'RANK_angle', 'LANK_angle',
                'RKNEE_angle_speed', 'LKNEE_angle_speed', 'RANK_angle_speed', 'LANK_angle_speed',
                'RKNEE_angle_acc', 'LKNEE_angle_acc', 'RANK_angle_acc', 'LANK_angle_acc']
    
    stats = {}
    
    for feature in features:
        if feature in data_df.columns:
            feature_data = data_df[feature].dropna()
            
            if len(feature_data) > 0:
                stats[f'{feature}_mean'] = np.mean(feature_data)
                stats[f'{feature}_std'] = np.std(feature_data)
                stats[f'{feature}_max'] = np.max(feature_data)
                stats[f'{feature}_min'] = np.min(feature_data)
                stats[f'{feature}_range'] = np.max(feature_data) - np.min(feature_data)
                stats[f'{feature}_median'] = np.median(feature_data)
                stats[f'{feature}_q25'] = np.percentile(feature_data, 25)
                stats[f'{feature}_q75'] = np.percentile(feature_data, 75)
                stats[f'{feature}_iqr'] = np.percentile(feature_data, 75) - np.percentile(feature_data, 25)
            else:
                # 如果数据为空，填充NaN
                for stat_type in ['mean', 'std', 'max', 'min', 'range', 'median', 'q25', 'q75', 'iqr']:
                    stats[f'{feature}_{stat_type}'] = np.nan
    
    return stats

def process_joint_features():
    """处理关节特征数据"""
    base_path = r"D:\论文\中期\第四章\数据\VICON\vicon_joint"
    output_path = r"D:\论文\中期\第四章\数据\VICON\joint_statistics"
    
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    
    all_statistics = []
    processing_log = []
    
    # 定义三类人群标签映射
    group_labels = {'healthy': 0, 'PD_early': 1, 'PD_mid': 2}
    
    print("开始处理三类人群关节特征数据...")
    print(f"数据路径: {base_path}")
    print(f"输出路径: {output_path}")
    
    # 遍历三个组别
    for group_name in ['healthy', 'PD_early', 'PD_mid']:
        group_path = os.path.join(base_path, group_name)
        
        if not os.path.exists(group_path):
            print(f"警告: 路径不存在 {group_path}")
            continue
        
        print(f"\n处理组别: {group_name} (标签: {group_labels[group_name]})")
        group_label = group_labels[group_name]
        
        # 获取该组的所有CSV文件
        csv_files = [f for f in os.listdir(group_path) if f.endswith('.csv')]
        print(f"找到 {len(csv_files)} 个文件")
        
        for csv_file in tqdm(csv_files, desc=f"处理{group_name}数据"):
            file_path = os.path.join(group_path, csv_file)
            
            try:
                # 读取CSV文件
                data_df = pd.read_csv(file_path)
                
                if data_df.empty:
                    print(f"警告: 空文件 {file_path}")
                    processing_log.append({
                        'file': file_path,
                        'status': 'empty',
                        'group': group_name,
                        'subject': 'unknown',
                        'trial': csv_file
                    })
                    continue
                
                # 从文件名提取受试者信息
                subject_id = csv_file.split('_')[0] if '_' in csv_file else 'unknown'
                trial_name = csv_file.replace('_joint.csv', '').replace('.csv', '')
                
                # 计算统计特征
                trial_stats = calculate_trial_statistics(data_df)
                
                # 添加元信息
                trial_stats['group_name'] = group_name
                trial_stats['group_label'] = group_label
                trial_stats['subject_id'] = subject_id
                trial_stats['trial_name'] = trial_name
                trial_stats['file_path'] = file_path
                trial_stats['frame_count'] = len(data_df)
                
                all_statistics.append(trial_stats)
                
                processing_log.append({
                    'file': file_path,
                    'status': 'success',
                    'group': group_name,
                    'subject': subject_id,
                    'trial': trial_name,
                    'frame_count': len(data_df)
                })
                
            except Exception as e:
                print(f"处理文件错误 {file_path}: {e}")
                processing_log.append({
                    'file': file_path,
                    'status': 'error',
                    'group': group_name,
                    'subject': 'unknown',
                    'trial': csv_file,
                    'error': str(e)
                })
    
    # 转换为DataFrame
    if all_statistics:
        stats_df = pd.DataFrame(all_statistics)
        
        # 保存完整统计数据
        full_stats_file = os.path.join(output_path, "all_joint_trial_statistics.csv")
        stats_df.to_csv(full_stats_file, index=False, encoding='utf-8-sig')
        print(f"完整统计数据已保存: {full_stats_file}")
        
        # 按组别保存
        for group_name, group_label in group_labels.items():
            group_data = stats_df[stats_df['group_label'] == group_label]
            if not group_data.empty:
                group_file = os.path.join(output_path, f"{group_name}_statistics.csv")
                group_data.to_csv(group_file, index=False, encoding='utf-8-sig')
                print(f"{group_name} 统计数据已保存: {group_file}")
        
        # 生成汇总统计
        generate_summary_statistics(stats_df, output_path)
        
        # 保存处理日志
        log_df = pd.DataFrame(processing_log)
        log_file = os.path.join(output_path, "processing_log.csv")
        log_df.to_csv(log_file, index=False, encoding='utf-8-sig')
        
        print(f"\n处理完成!")
        print(f"总处理文件数: {len(processing_log)}")
        print(f"成功处理: {sum(1 for log in processing_log if log['status'] == 'success')}")
        print(f"失败文件: {sum(1 for log in processing_log if log['status'] != 'success')}")
        print(f"健康人群样本数: {len(stats_df[stats_df['group_label'] == 0])}")
        print(f"早期PD患者样本数: {len(stats_df[stats_df['group_label'] == 1])}")
        print(f"中期PD患者样本数: {len(stats_df[stats_df['group_label'] == 2])}")
        
        return stats_df
    else:
        print("没有成功处理任何文件!")
        return None

def generate_summary_statistics(stats_df, output_path):
    """生成汇总统计报告"""
    
    # 获取所有特征列（排除元信息列）
    meta_cols = ['group_name', 'group_label', 'subject_id', 'trial_name', 'file_path', 'frame_count']
    feature_cols = [col for col in stats_df.columns if col not in meta_cols]
    
    # 按组别计算汇总统计
    summary_stats = []
    
    for group_label in [0, 1, 2]:
        group_name = 'healthy' if group_label == 0 else ('PD_early' if group_label == 1 else 'PD_mid')
        group_data = stats_df[stats_df['group_label'] == group_label]
        
        if group_data.empty:
            continue
        
        for feature in feature_cols:
            feature_data = group_data[feature].dropna()
            
            if len(feature_data) > 0:
                summary_stats.append({
                    'group_name': group_name,
                    'group_label': group_label,
                    'feature': feature,
                    'sample_count': len(feature_data),
                    'mean': np.mean(feature_data),
                    'std': np.std(feature_data),
                    'min': np.min(feature_data),
                    'max': np.max(feature_data),
                    'range': np.max(feature_data) - np.min(feature_data),
                    'median': np.median(feature_data),
                    'q25': np.percentile(feature_data, 25),
                    'q75': np.percentile(feature_data, 75),
                    'iqr': np.percentile(feature_data, 75) - np.percentile(feature_data, 25)
                })
    
    # 保存汇总统计
    if summary_stats:
        summary_df = pd.DataFrame(summary_stats)
        summary_file = os.path.join(output_path, "group_feature_summary.csv")
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
        print(f"汇总统计已保存: {summary_file}")
        
        # 生成对比报告
        generate_comparison_report(summary_df, output_path)

def generate_comparison_report(summary_df, output_path):
    """生成三组间对比报告"""
    
    # 获取所有特征
    features = summary_df['feature'].unique()
    
    comparison_results = []
    
    for feature in features:
        feature_data = summary_df[summary_df['feature'] == feature]
        
        # 获取三个组的数据
        healthy_data = feature_data[feature_data['group_label'] == 0]
        pd_early_data = feature_data[feature_data['group_label'] == 1]
        pd_mid_data = feature_data[feature_data['group_label'] == 2]
        
        if len(healthy_data) > 0 and len(pd_early_data) > 0 and len(pd_mid_data) > 0:
            healthy_row = healthy_data.iloc[0]
            pd_early_row = pd_early_data.iloc[0]
            pd_mid_row = pd_mid_data.iloc[0]
            
            # 计算组间差异
            early_vs_healthy_diff = pd_early_row['mean'] - healthy_row['mean']
            mid_vs_healthy_diff = pd_mid_row['mean'] - healthy_row['mean']
            mid_vs_early_diff = pd_mid_row['mean'] - pd_early_row['mean']
            
            # 计算百分比差异
            early_vs_healthy_pct = (early_vs_healthy_diff / healthy_row['mean']) * 100 if healthy_row['mean'] != 0 else np.inf
            mid_vs_healthy_pct = (mid_vs_healthy_diff / healthy_row['mean']) * 100 if healthy_row['mean'] != 0 else np.inf
            mid_vs_early_pct = (mid_vs_early_diff / pd_early_row['mean']) * 100 if pd_early_row['mean'] != 0 else np.inf
            
            comparison_results.append({
                'feature': feature,
                'healthy_mean': healthy_row['mean'],
                'healthy_std': healthy_row['std'],
                'healthy_range': healthy_row['range'],
                'pd_early_mean': pd_early_row['mean'],
                'pd_early_std': pd_early_row['std'],
                'pd_early_range': pd_early_row['range'],
                'pd_mid_mean': pd_mid_row['mean'],
                'pd_mid_std': pd_mid_row['std'],
                'pd_mid_range': pd_mid_row['range'],
                'early_vs_healthy_diff': early_vs_healthy_diff,
                'early_vs_healthy_pct': early_vs_healthy_pct,
                'mid_vs_healthy_diff': mid_vs_healthy_diff,
                'mid_vs_healthy_pct': mid_vs_healthy_pct,
                'mid_vs_early_diff': mid_vs_early_diff,
                'mid_vs_early_pct': mid_vs_early_pct
            })
    
    # 保存对比结果
    if comparison_results:
        comparison_df = pd.DataFrame(comparison_results)
        comparison_file = os.path.join(output_path, "three_group_comparison.csv")
        comparison_df.to_csv(comparison_file, index=False, encoding='utf-8-sig')
        print(f"三组对比结果已保存: {comparison_file}")
        
        # 生成文本报告
        generate_text_report(comparison_df, output_path)

def generate_text_report(comparison_df, output_path):
    """生成文本格式的报告"""
    
    report_lines = []
    report_lines.append("=== 三类人群关节特征对比报告 ===\n")
    report_lines.append("组别说明:")
    report_lines.append("- 健康人群 (healthy)")
    report_lines.append("- 早期帕金森病患者 (PD_early)")
    report_lines.append("- 中期帕金森病患者 (PD_mid)\n")
    
    # 按特征类型分组
    angle_features = comparison_df[comparison_df['feature'].str.contains('angle') & ~comparison_df['feature'].str.contains('speed|acc')]
    speed_features = comparison_df[comparison_df['feature'].str.contains('speed')]
    acc_features = comparison_df[comparison_df['feature'].str.contains('acc')]
    
    for feature_type, features_df in [("关节角度特征", angle_features), 
                                      ("关节角速度特征", speed_features), 
                                      ("关节角加速度特征", acc_features)]:
        if not features_df.empty:
            report_lines.append(f"\n=== {feature_type} ===")
            
            for _, row in features_df.iterrows():
                report_lines.append(f"\n{row['feature']}:")
                report_lines.append(f"  健康人群:     {row['healthy_mean']:.4f} ± {row['healthy_std']:.4f} (范围: {row['healthy_range']:.4f})")
                report_lines.append(f"  早期PD患者:   {row['pd_early_mean']:.4f} ± {row['pd_early_std']:.4f} (范围: {row['pd_early_range']:.4f})")
                report_lines.append(f"  中期PD患者:   {row['pd_mid_mean']:.4f} ± {row['pd_mid_std']:.4f} (范围: {row['pd_mid_range']:.4f})")
                report_lines.append(f"  早期 vs 健康: {row['early_vs_healthy_diff']:.4f} ({row['early_vs_healthy_pct']:.2f}%)")
                report_lines.append(f"  中期 vs 健康: {row['mid_vs_healthy_diff']:.4f} ({row['mid_vs_healthy_pct']:.2f}%)")
                report_lines.append(f"  中期 vs 早期: {row['mid_vs_early_diff']:.4f} ({row['mid_vs_early_pct']:.2f}%)")
    
    # 保存文本报告
    report_file = os.path.join(output_path, "three_group_comparison_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"文本报告已保存: {report_file}")

if __name__ == "__main__":
    # 执行特征统计分析
    stats_df = process_joint_features()
