import pandas as pd
import numpy as np
import os
import warnings
warnings.filterwarnings('ignore')

def load_trial_data():
    """加载试验层级的统计数据"""
    base_path = r"D:\论文\中期\第四章\数据\VICON\joint_statistics"
    stats_file = os.path.join(base_path, "all_joint_trial_statistics.csv")

    if not os.path.exists(stats_file):
        print(f"数据文件不存在: {stats_file}")
        return None

    stats_df = pd.read_csv(stats_file)

    # 检查不同组间是否存在相同的subject_id
    print("=== 数据质量检查 ===")
    print(f"加载试验数据: {len(stats_df)} 条记录")
    print(f"总受试者ID数量: {stats_df['subject_id'].nunique()} 个")

    # 检查跨组重复的subject_id
    subject_group_mapping = stats_df.groupby('subject_id')['group_label'].unique()
    duplicate_subjects = subject_group_mapping[subject_group_mapping.apply(len) > 1]

    if len(duplicate_subjects) > 0:
        print(f"\n⚠️  警告: 发现 {len(duplicate_subjects)} 个subject_id在多个组中出现:")
        for subject_id, groups in duplicate_subjects.items():
            group_names = [['健康', '早期PD', '中期PD'][g] for g in groups]
            print(f"  Subject {subject_id}: 出现在 {group_names}")

        print("\n正在创建唯一标识符...")
        # 创建组合唯一标识符
        stats_df['unique_subject_id'] = stats_df['group_name'] + '_' + stats_df['subject_id'].astype(str)
        print("已创建 'unique_subject_id' 字段: group_name + subject_id")
    else:
        print("✓ 未发现跨组重复的subject_id")
        stats_df['unique_subject_id'] = stats_df['subject_id'].astype(str)

    print(f"\n各组分布: ")
    for group_label, group_name in [(0, '健康'), (1, '早期PD'), (2, '中期PD')]:
        group_data = stats_df[stats_df['group_label'] == group_label]
        count = len(group_data)
        subjects = group_data['unique_subject_id'].nunique()
        original_subjects = group_data['subject_id'].nunique()
        print(f"  {group_name}: {count} 条试验记录, {subjects} 名唯一受试者 (原始ID: {original_subjects})")

    return stats_df

def aggregate_subject_features(stats_df):
    """将同一受试者的多次试验特征进行平均，生成受试者层级特征"""

    # 定义需要聚合的特征列
    feature_columns = [col for col in stats_df.columns
                      if any(keyword in col for keyword in ['_mean', '_std', '_max', '_min', '_range', '_median', '_q25', '_q75', '_iqr'])]

    print(f"识别到 {len(feature_columns)} 个特征列需要聚合")

    # 按唯一受试者ID分组并计算平均值
    subject_aggregated = []

    for unique_subject_id in stats_df['unique_subject_id'].unique():
        subject_data = stats_df[stats_df['unique_subject_id'] == unique_subject_id]

        # 验证该唯一ID下的数据一致性
        if subject_data['group_label'].nunique() > 1:
            print(f"⚠️  警告: 唯一ID {unique_subject_id} 下存在多个组别，跳过该受试者")
            continue

        # 获取受试者的基本信息（取第一条记录的信息）
        subject_info = {
            'unique_subject_id': unique_subject_id,
            'original_subject_id': subject_data['subject_id'].iloc[0],
            'group_name': subject_data['group_name'].iloc[0],
            'group_label': subject_data['group_label'].iloc[0],
            'trial_count': len(subject_data)  # 该受试者的试验次数
        }
        
        # 计算各特征的平均值
        for feature in feature_columns:
            if feature in subject_data.columns:
                feature_values = subject_data[feature].dropna()
                if len(feature_values) > 0:
                    # 计算平均值
                    subject_info[f'{feature}_avg'] = np.mean(feature_values)
                    # 计算标准差（受试者内变异性）
                    subject_info[f'{feature}_within_std'] = np.std(feature_values, ddof=1) if len(feature_values) > 1 else 0
                    # 计算变异系数
                    if np.mean(feature_values) != 0:
                        subject_info[f'{feature}_cv'] = np.std(feature_values, ddof=1) / np.mean(feature_values) if len(feature_values) > 1 else 0
                    else:
                        subject_info[f'{feature}_cv'] = np.nan
                else:
                    subject_info[f'{feature}_avg'] = np.nan
                    subject_info[f'{feature}_within_std'] = np.nan
                    subject_info[f'{feature}_cv'] = np.nan
        
        subject_aggregated.append(subject_info)
    
    subject_df = pd.DataFrame(subject_aggregated)
    
    print(f"生成受试者层级数据: {len(subject_df)} 名受试者")
    print(f"各组受试者数量:")
    for group_label, group_name in [(0, '健康'), (1, '早期PD'), (2, '中期PD')]:
        count = len(subject_df[subject_df['group_label'] == group_label])
        print(f"  {group_name}: {count} 名受试者")
    
    return subject_df

def generate_subject_summary_statistics(subject_df):
    """生成受试者层级的汇总统计"""

    # 获取特征列（排除元信息列）
    meta_cols = ['unique_subject_id', 'original_subject_id', 'group_name', 'group_label', 'trial_count']
    feature_cols = [col for col in subject_df.columns if col not in meta_cols and col.endswith('_avg')]
    
    summary_stats = []
    
    for group_label in [0, 1, 2]:
        group_name = '健康' if group_label == 0 else ('早期PD' if group_label == 1 else '中期PD')
        group_data = subject_df[subject_df['group_label'] == group_label]
        
        if group_data.empty:
            continue
        
        for feature in feature_cols:
            feature_data = group_data[feature].dropna()
            
            if len(feature_data) > 0:
                summary_stats.append({
                    'group_name': group_name,
                    'group_label': group_label,
                    'feature': feature.replace('_avg', ''),
                    'subject_count': len(feature_data),
                    'mean': np.mean(feature_data),
                    'std': np.std(feature_data, ddof=1),
                    'min': np.min(feature_data),
                    'max': np.max(feature_data),
                    'range': np.max(feature_data) - np.min(feature_data),
                    'median': np.median(feature_data),
                    'q25': np.percentile(feature_data, 25),
                    'q75': np.percentile(feature_data, 75),
                    'iqr': np.percentile(feature_data, 75) - np.percentile(feature_data, 25),
                    'cv': np.std(feature_data, ddof=1) / np.mean(feature_data) if np.mean(feature_data) != 0 else np.nan,
                    'sem': np.std(feature_data, ddof=1) / np.sqrt(len(feature_data))  # 标准误
                })
    
    summary_df = pd.DataFrame(summary_stats)
    return summary_df

def generate_subject_comparison_report(subject_df, summary_df):
    """生成受试者层级的对比报告"""

    report_lines = []
    report_lines.append("=== 受试者层级关节特征对比报告 ===\n")
    report_lines.append("数据说明:")
    report_lines.append("- 将同一受试者的多次试验数据进行平均，得到受试者代表性特征")
    report_lines.append("- 消除了受试者内部试验间的变异，更好地反映个体差异")
    report_lines.append("- 使用unique_subject_id确保跨组数据的唯一性")
    report_lines.append("- 适用于组间比较和个体特征分析\n")
    
    # 受试者基本信息统计
    report_lines.append("=== 受试者基本信息 ===")
    for group_label, group_name in [(0, '健康'), (1, '早期PD'), (2, '中期PD')]:
        group_data = subject_df[subject_df['group_label'] == group_label]
        if not group_data.empty:
            avg_trials = group_data['trial_count'].mean()
            min_trials = group_data['trial_count'].min()
            max_trials = group_data['trial_count'].max()
            report_lines.append(f"{group_name}组: {len(group_data)} 名受试者")
            report_lines.append(f"  平均试验次数: {avg_trials:.1f} (范围: {min_trials}-{max_trials})")
    
    report_lines.append("")
    
    # 主要特征对比
    main_features = ['RKNEE_angle_mean', 'LKNEE_angle_mean', 'RANK_angle_mean', 'LANK_angle_mean']
    
    report_lines.append("=== 主要关节角度特征对比 ===")
    for feature in main_features:
        feature_summary = summary_df[summary_df['feature'] == feature]
        if not feature_summary.empty:
            report_lines.append(f"\n【{feature.replace('_mean', '')}】")
            for _, row in feature_summary.iterrows():
                report_lines.append(f"{row['group_name']}: {row['mean']:.4f}±{row['std']:.4f} " +
                                  f"(n={row['subject_count']}, 范围:{row['min']:.4f}-{row['max']:.4f})")
    
    # 变异性分析
    report_lines.append("\n=== 组内变异性分析 ===")
    for feature in main_features:
        feature_summary = summary_df[summary_df['feature'] == feature]
        if not feature_summary.empty:
            report_lines.append(f"\n【{feature.replace('_mean', '')} 变异系数】")
            for _, row in feature_summary.iterrows():
                cv_percent = row['cv'] * 100 if not np.isnan(row['cv']) else 0
                report_lines.append(f"{row['group_name']}: {cv_percent:.2f}%")
    
    return '\n'.join(report_lines)

def main():
    """主函数"""
    print("开始生成受试者层级统计汇总...")
    
    # 1. 加载试验层级数据
    stats_df = load_trial_data()
    if stats_df is None:
        return
    
    # 2. 聚合生成受试者层级特征
    print("\n正在聚合受试者层级特征...")
    subject_df = aggregate_subject_features(stats_df)
    
    # 3. 生成受试者层级汇总统计
    print("\n正在生成汇总统计...")
    summary_df = generate_subject_summary_statistics(subject_df)
    
    # 4. 创建输出目录
    output_path = r"D:\论文\中期\第四章\数据\VICON\subject_level_analysis"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    
    # 5. 保存结果
    print(f"\n正在保存结果到: {output_path}")
    
    # 保存受试者层级数据
    subject_file = os.path.join(output_path, "subject_level_features.csv")
    subject_df.to_csv(subject_file, index=False, encoding='utf-8-sig')
    print(f"受试者层级特征已保存: {subject_file}")
    
    # 保存汇总统计
    summary_file = os.path.join(output_path, "subject_level_summary.csv")
    summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
    print(f"汇总统计已保存: {summary_file}")
    
    # 按组别保存受试者数据
    for group_label, group_name in [(0, '健康'), (1, '早期PD'), (2, '中期PD')]:
        group_data = subject_df[subject_df['group_label'] == group_label]
        if not group_data.empty:
            group_file = os.path.join(output_path, f"subject_level_{group_name}.csv")
            group_data.to_csv(group_file, index=False, encoding='utf-8-sig')
            print(f"{group_name}组受试者数据已保存: {group_file}")
    
    # 生成对比报告
    print("\n正在生成对比报告...")
    report_content = generate_subject_comparison_report(subject_df, summary_df)
    report_file = os.path.join(output_path, "subject_level_comparison_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    print(f"对比报告已保存: {report_file}")
    
    # 输出汇总信息
    print(f"\n=== 处理完成 ===")
    print(f"数据质量说明:")
    print(f"- 使用 'unique_subject_id' 作为主键，确保跨组唯一性")
    print(f"- 保留 'original_subject_id' 用于追溯原始数据")
    print(f"- 已处理可能存在的跨组ID重复问题")

    print(f"\n输出文件:")
    print(f"- subject_level_features.csv: 受试者层级特征数据 ({len(subject_df)} 名唯一受试者)")
    print(f"- subject_level_summary.csv: 汇总统计数据")
    print(f"- subject_level_健康.csv: 健康组受试者数据")
    print(f"- subject_level_早期PD.csv: 早期PD组受试者数据")
    print(f"- subject_level_中期PD.csv: 中期PD组受试者数据")
    print(f"- subject_level_comparison_report.txt: 详细对比报告")

    print(f"\n字段说明:")
    print(f"- unique_subject_id: 组合唯一标识符 (group_name + original_subject_id)")
    print(f"- original_subject_id: 原始受试者ID")
    print(f"- 特征字段以 '_avg' 结尾表示该受试者多次试验的平均值")

    print(f"\n这些数据可用于:")
    print(f"- 组间统计检验（消除受试者内变异和ID冲突）")
    print(f"- 个体特征分析和可视化")
    print(f"- 机器学习建模（每个受试者一个样本）")
    print(f"- 临床特征关联分析")

if __name__ == "__main__":
    main()
