import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import NMF
import seaborn as sns
from tqdm import tqdm
import warnings
import re
warnings.filterwarnings('ignore')

'''
说明：
    根据已经用Vicon切分完 且标准化的 肌电数据进行肌肉协同分析。

'''


# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def extract_person_name_from_filename(filename):
    """
    从CSV文件名中提取人名（Cal前的部分）
    """
    # 移除文件扩展名
    basename = os.path.splitext(filename)[0]
    
    # 查找"Cal"的位置
    cal_match = re.search(r'Cal', basename, re.IGNORECASE)
    if cal_match:
        # 提取Cal前的部分
        person_part = basename[:cal_match.start()].strip()
        # 移除可能的下划线或空格
        person_part = person_part.rstrip('_').strip()
        return person_part
    
    return None

def find_optimal_components_vaf(X, max_components=8, variance_threshold=0.85):
    """
    使用VAF方法找到最优组件数
    """
    results = {}
    
    # 计算每个组件数的方差解释
    variance_explained = []
    reconstruction_errors = []
    
    for n_comp in range(1, max_components + 1):
        nmf = NMF(n_components=n_comp, random_state=42, max_iter=1000)
        W = nmf.fit_transform(X)
        H = nmf.components_
        
        # 计算重构误差
        X_reconstructed = np.dot(W, H)
        error = np.sum((X - X_reconstructed) ** 2)
        reconstruction_errors.append(error)
        
        # 计算方差解释率(VAF)
        total_variance = np.sum(X ** 2)
        explained_variance = 1 - (error / total_variance)
        variance_explained.append(explained_variance)
    
    # 找到VAF肘点
    vaf_elbow = find_vaf_elbow(variance_explained)
    
    # 找到满足方差阈值的最小组件数
    variance_threshold_n = max_components
    for i, var_exp in enumerate(variance_explained):
        if var_exp >= variance_threshold:
            variance_threshold_n = i + 1
            break
    
    # 组合标准：max(VAF肘点, 方差阈值)
    combined_optimal = max(vaf_elbow, variance_threshold_n)
    
    results = {
        'variance_explained': variance_explained,
        'reconstruction_errors': reconstruction_errors,
        'vaf_elbow': vaf_elbow,
        'variance_threshold_n': variance_threshold_n,
        'combined_optimal': combined_optimal,
        'recommended_n': combined_optimal,
        'variance_threshold': variance_threshold
    }
    
    return results

def find_vaf_elbow(variance_explained, threshold=0.05):
    """
    找到VAF曲线的肘点
    """
    if len(variance_explained) < 3:
        return 1
    
    # 计算一阶导数（斜率）
    slopes = np.diff(variance_explained)
    
    # 计算二阶导数（斜率变化）
    slope_changes = np.diff(slopes)
    
    # 找到肘点
    elbow_idx = 1  # 从组件2开始（索引1）
    
    # 方法1：找到斜率小于阈值的点
    for i, slope in enumerate(slopes):
        if slope <= threshold:
            elbow_idx = i + 1
            break
    
    # 方法2：找到最大曲率（最负的二阶导数）
    if len(slope_changes) > 0:
        max_curvature_idx = np.argmin(slope_changes) + 2
        # 使用更保守的估计
        elbow_idx = max(elbow_idx, max_curvature_idx)
    
    return min(elbow_idx, len(variance_explained))

def perform_nmf_analysis(X, n_components, random_state=42):
    """
    执行NMF分析
    """
    nmf = NMF(n_components=n_components, random_state=random_state, max_iter=1000)
    W = nmf.fit_transform(X)  # 激活系数（时间 x 协同）
    H = nmf.components_      # 协同模式（协同 x 肌肉）
    
    # 计算重构质量
    X_reconstructed = np.dot(W, H)
    reconstruction_error = np.sum((X - X_reconstructed) ** 2)
    total_variance = np.sum(X ** 2)
    variance_explained = 1 - (reconstruction_error / total_variance)
    
    return {
        'activations': W,
        'synergies': H,
        'reconstruction_error': reconstruction_error,
        'variance_explained': variance_explained,
        'reconstructed_data': X_reconstructed
    }

def process_csv_files_by_time_and_group():
    """
    主函数：按时间和组别处理CSV文件
    """
    # 定义路径
    base_path = r"D:\论文\中期\第四章\数据\模式\L"
    output_base_path = r"D:\论文\中期\第四章\数据\模式\synergy_analysis_by_groups"
    
    # 创建输出目录
    os.makedirs(output_base_path, exist_ok=True)
    
    # EMG通道名称
    emg_channels = ['EMG1', 'EMG2', 'EMG3', 'EMG4', 'EMG5', 'EMG6', 'EMG7', 'EMG8']
    
    # 时间阶段
    time_phases = ['0', '1', '2', '3', '4', '5', '6']
    
    # 人群类别映射
    group_mapping = {
        'healthy': 0,
        'PDearly': 1, 
        'PDmid': 2
    }
    
    # 结果存储
    all_results = {}
    processing_summary = {}
    
    print("开始按时间和组别进行肌肉协同分析...")
    
    # 处理每个时间阶段
    for time_phase in time_phases:
        print(f"\n处理时间阶段 {time_phase}...")
        
        all_results[time_phase] = {}
        processing_summary[time_phase] = {}
        
        # 初始化组别数据存储
        group_data = {
            'healthy': [],
            'PDearly': [],
            'PDmid': []
        }
        
        # 扫描所有组别文件夹
        for folder_name in os.listdir(base_path):
            folder_path = os.path.join(base_path, folder_name)
            
            if not os.path.isdir(folder_path):
                continue
            
            # 解析文件夹名称：group_time
            if '_' not in folder_name:
                continue
                
            group_name, folder_time = folder_name.split('_', 1)
            
            # 只处理当前时间阶段
            if folder_time != time_phase:
                continue
            
            # 检查是否是有效的组别
            if group_name not in group_mapping:
                print(f"    未知组别: {group_name}")
                continue
            
            print(f"    处理组别 {group_name} (时间阶段 {time_phase})...")
            
            # 获取该组别的所有CSV文件
            csv_files = [f for f in os.listdir(folder_path) if f.endswith('.csv')]
            
            if not csv_files:
                print(f"      组别 {group_name} 中没有CSV文件")
                continue
            
            print(f"      找到 {len(csv_files)} 个CSV文件")
            
            # 处理每个CSV文件
            for csv_file in csv_files:
                file_path = os.path.join(folder_path, csv_file)
                
                try:
                    # 读取CSV数据
                    data = pd.read_csv(file_path)
                    
                    # 检查是否包含EMG数据
                    missing_channels = [ch for ch in emg_channels if ch not in data.columns]
                    if missing_channels:
                        print(f"        {csv_file}: 缺少EMG通道 {missing_channels}")
                        continue
                    
                    # 提取EMG数据
                    emg_data = data[emg_channels].values
                    
                    if emg_data.shape[0] < 10:  # 跳过数据点太少的文件
                        print(f"        {csv_file}: 数据点不足 ({emg_data.shape[0]})")
                        continue
                    
                    # 确保数据非负（NMF要求）
                    emg_data = np.maximum(emg_data, 0)
                    
                    # 添加小的epsilon避免零值
                    emg_data = emg_data + 1e-10
                    
                    # 提取人名和组别标签
                    person_name = extract_person_name_from_filename(csv_file)
                    group_label = group_mapping[group_name]
                    
                    # 存储数据
                    group_data[group_name].append({
                        'emg_data': emg_data,
                        'filename': csv_file,
                        'person_name': person_name,
                        'group_label': group_label,
                        'file_path': file_path
                    })
                    
                except Exception as e:
                    print(f"        处理 {csv_file} 时出错: {str(e)}")
                    continue
        
        # 记录处理摘要
        processing_summary[time_phase] = {
            'healthy': len(group_data['healthy']),
            'PDearly': len(group_data['PDearly']),
            'PDmid': len(group_data['PDmid']),
            'total': sum(len(group_data[g]) for g in group_data)
        }
        
        print(f"    时间阶段 {time_phase} 分组结果:")
        print(f"      健康组: {len(group_data['healthy'])} 文件")
        print(f"      早期PD组: {len(group_data['PDearly'])} 文件") 
        print(f"      中期PD组: {len(group_data['PDmid'])} 文件")
        
        # 处理每个组别的数据
        for group_name in ['healthy', 'PDearly', 'PDmid']:
            if not group_data[group_name]:
                print(f"      组别 {group_name}: 无数据")
                continue
            
            print(f"      处理组别 {group_name} ({len(group_data[group_name])} 个文件)...")
            
            # 创建组输出目录
            group_output_dir = os.path.join(output_base_path, f"time{time_phase}_{group_name}")
            os.makedirs(group_output_dir, exist_ok=True)
            
            # 初始化组结果
            all_results[time_phase][group_name] = {}
            
            # 处理组内每个文件
            for file_data in tqdm(group_data[group_name], desc=f"时间{time_phase}_{group_name}"):
                emg_data = file_data['emg_data']
                filename = file_data['filename']
                person_name = file_data['person_name']
                group_label = file_data['group_label']
                
                # 找到最优组件数
                optimization_results = find_optimal_components_vaf(
                    emg_data, max_components=min(8, emg_data.shape[1])
                )
                
                # 获取文件标识符
                file_id = os.path.splitext(filename)[0]
                
                # 执行NMF分析
                recommended_n = optimization_results['recommended_n']
                nmf_results = perform_nmf_analysis(emg_data, recommended_n)
                
                # 存储结果
                all_results[time_phase][group_name][file_id] = {
                    'optimization': optimization_results,
                    'nmf': nmf_results,
                    'file_info': {
                        'filename': filename,
                        'person_name': person_name,
                        'group_label': group_label,
                        'data_shape': emg_data.shape
                    }
                }
                
                # 保存个体结果
                save_individual_results(
                    nmf_results, optimization_results, file_id, 
                    group_output_dir, emg_channels, time_phase, group_name, group_label
                )
                
                print(f"          {file_id}: {recommended_n} 组件, VAF: {nmf_results['variance_explained']:.3f}")
            
            # 创建组汇总
            create_group_summary(
                all_results[time_phase][group_name], 
                group_output_dir, f"time{time_phase}_{group_name}", group_mapping[group_name]
            )
    
    # 创建总体汇总报告
    create_overall_summary(all_results, processing_summary, output_base_path)
    
    print(f"\n肌肉协同分析完成!")
    print(f"结果保存到: {output_base_path}")

def save_individual_results(nmf_results, optimization_results, file_id, output_dir, emg_channels, time_phase, group_name, group_label):
    """
    保存个体分析结果
    """
    # 创建个体文件输出目录
    file_output_dir = os.path.join(output_dir, file_id)
    os.makedirs(file_output_dir, exist_ok=True)

    recommended_n = optimization_results['recommended_n']

    # 保存协同模式
    synergy_patterns_df = pd.DataFrame(
        nmf_results['synergies'],
        columns=emg_channels,
        index=[f'Synergy_{i+1}' for i in range(recommended_n)]
    )
    # 添加组别标签列
    synergy_patterns_df['group_label'] = group_label
    synergy_patterns_df['group_name'] = group_name
    synergy_patterns_df['time_phase'] = time_phase
    synergy_patterns_df.to_csv(os.path.join(file_output_dir, "synergy_patterns.csv"))

    # 保存激活系数
    activations_df = pd.DataFrame(
        nmf_results['activations'],
        columns=[f'Synergy_{i+1}' for i in range(recommended_n)]
    )
    # 添加组别标签列
    activations_df['group_label'] = group_label
    activations_df['group_name'] = group_name
    activations_df['time_phase'] = time_phase
    activations_df.to_csv(os.path.join(file_output_dir, "synergy_activations.csv"), index=False)

    # 保存优化详情
    optimization_df = pd.DataFrame({
        'n_components': range(1, len(optimization_results['variance_explained']) + 1),
        'variance_explained': optimization_results['variance_explained'],
        'reconstruction_error': optimization_results['reconstruction_errors']
    })
    optimization_df['group_label'] = group_label
    optimization_df['group_name'] = group_name
    optimization_df['time_phase'] = time_phase
    optimization_df.to_csv(os.path.join(file_output_dir, "vaf_analysis_results.csv"), index=False)

    # 绘制协同模式热图
    plt.figure(figsize=(12, 8))
    sns.heatmap(synergy_patterns_df[emg_channels], annot=True, cmap='viridis', cbar=True)
    plt.title(f'肌肉协同模式 - 时间{time_phase} {group_name} {file_id}\n'
             f'({recommended_n} 组件, VAF: {nmf_results["variance_explained"]:.3f})')
    plt.xlabel('EMG通道')
    plt.ylabel('协同组件')
    plt.tight_layout()
    plt.savefig(os.path.join(file_output_dir, "synergy_patterns_heatmap.png"), dpi=300, bbox_inches='tight')
    plt.close()

def create_group_summary(group_results, output_dir, group_name, group_label):
    """
    创建组汇总统计
    """
    if not group_results:
        return

    # 收集组内统计信息
    recommended_components = []
    variance_explained = []
    vaf_elbows = []
    file_ids = []
    person_names = []

    for file_id, results in group_results.items():
        opt_res = results['optimization']
        nmf_res = results['nmf']
        file_info = results['file_info']

        recommended_components.append(opt_res['recommended_n'])
        variance_explained.append(nmf_res['variance_explained'])
        vaf_elbows.append(opt_res['vaf_elbow'])
        file_ids.append(file_id)
        person_names.append(file_info['person_name'])

    # 创建汇总DataFrame
    summary_df = pd.DataFrame({
        'file_id': file_ids,
        'person_name': person_names,
        'recommended_components': recommended_components,
        'variance_explained': variance_explained,
        'vaf_elbow': vaf_elbows,
        'group_label': group_label,
        'group_name': group_name.split('_')[1] if '_' in group_name else group_name
    })

    # 保存组汇总
    summary_df.to_csv(os.path.join(output_dir, "group_summary.csv"), index=False)

    # 计算组统计
    group_stats = {
        'mean_components': np.mean(recommended_components),
        'std_components': np.std(recommended_components),
        'mean_vaf': np.mean(variance_explained),
        'std_vaf': np.std(variance_explained),
        'file_count': len(group_results)
    }

    # 保存组统计
    with open(os.path.join(output_dir, "group_statistics.txt"), 'w', encoding='utf-8') as f:
        f.write(f"=== {group_name} 组统计 ===\n\n")
        f.write(f"组别标签: {group_label}\n")
        f.write(f"文件数量: {group_stats['file_count']}\n")
        f.write(f"平均组件数: {group_stats['mean_components']:.2f} ± {group_stats['std_components']:.2f}\n")
        f.write(f"平均VAF: {group_stats['mean_vaf']:.3f} ± {group_stats['std_vaf']:.3f}\n")
        f.write(f"组件数范围: {min(recommended_components)}-{max(recommended_components)}\n")

def create_overall_summary(all_results, processing_summary, output_base_path):
    """
    创建总体汇总报告
    """
    summary_path = os.path.join(output_base_path, "overall_analysis_summary.txt")

    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write("=== 三类人群肌肉协同分析总体汇总 ===\n\n")

        f.write("分析参数:\n")
        f.write("- 方法: 非负矩阵分解 (NMF)\n")
        f.write("- 组件选择: VAF肘点 + 85%方差阈值\n")
        f.write("- 最大组件数: 8\n")
        f.write("- EMG通道: EMG1-EMG8\n")
        f.write("- 人群分类: healthy(0), PDearly(1), PDmid(2)\n\n")

        f.write("处理摘要:\n")
        for time_phase in all_results:
            f.write(f"\n时间阶段 {time_phase}:\n")

            if time_phase in processing_summary:
                summary = processing_summary[time_phase]
                f.write(f"  总文件数: {summary['total']}\n")
                f.write(f"  健康组: {summary['healthy']} 文件\n")
                f.write(f"  早期PD组: {summary['PDearly']} 文件\n")
                f.write(f"  中期PD组: {summary['PDmid']} 文件\n")

            for group in all_results[time_phase]:
                group_results = all_results[time_phase][group]
                if group_results:
                    components = [group_results[file_id]['optimization']['recommended_n']
                                for file_id in group_results]
                    vafs = [group_results[file_id]['nmf']['variance_explained']
                           for file_id in group_results]

                    f.write(f"  {group} ({len(group_results)} 文件):\n")
                    f.write(f"    平均组件数: {np.mean(components):.2f} ± {np.std(components):.2f}\n")
                    f.write(f"    平均VAF: {np.mean(vafs):.3f} ± {np.std(vafs):.3f}\n")

    # 创建合并的汇总数据文件
    create_combined_summary_files(all_results, output_base_path)

def create_combined_summary_files(all_results, output_base_path):
    """
    创建合并的汇总数据文件，便于后续分析
    """
    # 合并所有时间阶段的数据
    all_synergy_patterns = []
    all_activations = []
    all_summaries = []

    for time_phase in all_results:
        for group_name in all_results[time_phase]:
            group_results = all_results[time_phase][group_name]

            for file_id, results in group_results.items():
                nmf_res = results['nmf']
                opt_res = results['optimization']
                file_info = results['file_info']

                # 协同模式数据
                synergy_df = pd.DataFrame(
                    nmf_res['synergies'],
                    columns=[f'EMG{i+1}' for i in range(8)]
                )
                synergy_df['file_id'] = file_id
                synergy_df['person_name'] = file_info['person_name']
                synergy_df['group_label'] = file_info['group_label']
                synergy_df['group_name'] = group_name
                synergy_df['time_phase'] = time_phase
                synergy_df['synergy_id'] = [f'Synergy_{i+1}' for i in range(len(synergy_df))]
                all_synergy_patterns.append(synergy_df)

                # 激活系数数据
                activation_df = pd.DataFrame(
                    nmf_res['activations'],
                    columns=[f'Synergy_{i+1}' for i in range(nmf_res['activations'].shape[1])]
                )
                activation_df['file_id'] = file_id
                activation_df['person_name'] = file_info['person_name']
                activation_df['group_label'] = file_info['group_label']
                activation_df['group_name'] = group_name
                activation_df['time_phase'] = time_phase
                all_activations.append(activation_df)

                # 汇总统计
                summary_row = {
                    'file_id': file_id,
                    'person_name': file_info['person_name'],
                    'group_label': file_info['group_label'],
                    'group_name': group_name,
                    'time_phase': time_phase,
                    'recommended_components': opt_res['recommended_n'],
                    'variance_explained': nmf_res['variance_explained'],
                    'vaf_elbow': opt_res['vaf_elbow'],
                    'data_points': file_info['data_shape'][0]
                }
                all_summaries.append(summary_row)

    # 保存合并的数据文件
    if all_synergy_patterns:
        combined_synergy_df = pd.concat(all_synergy_patterns, ignore_index=True)
        combined_synergy_df.to_csv(os.path.join(output_base_path, "all_synergy_patterns.csv"), index=False)

    if all_activations:
        combined_activation_df = pd.concat(all_activations, ignore_index=True)
        combined_activation_df.to_csv(os.path.join(output_base_path, "all_activations.csv"), index=False)

    if all_summaries:
        combined_summary_df = pd.DataFrame(all_summaries)
        combined_summary_df.to_csv(os.path.join(output_base_path, "all_analysis_summary.csv"), index=False)

        # 按时间阶段分组的汇总
        time_grouped_summary = combined_summary_df.groupby(['time_phase', 'group_name']).agg({
            'recommended_components': ['mean', 'std', 'count'],
            'variance_explained': ['mean', 'std'],
            'vaf_elbow': ['mean', 'std']
        }).round(3)
        time_grouped_summary.to_csv(os.path.join(output_base_path, "time_phase_group_summary.csv"))

        print(f"\n合并数据文件已保存:")
        print(f"- 协同模式: all_synergy_patterns.csv")
        print(f"- 激活系数: all_activations.csv")
        print(f"- 分析汇总: all_analysis_summary.csv")
        print(f"- 时间阶段汇总: time_phase_group_summary.csv")

if __name__ == "__main__":
    process_csv_files_by_time_and_group()
