import os
import pandas as pd
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from tqdm import tqdm

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def normalize_and_calculate_l1_norm_by_groups():
    """
    处理第四章三类人群的协同模式：标准化向量并计算L1范数
    """
    # 定义路径
    input_base_path = r"D:\论文\中期\第四章\数据\模式\synergy_analysis_by_groups"
    output_base_path = r"D:\论文\中期\第四章\数据\模式\synergy_patterns_processed_by_groups"
    
    # 创建输出目录
    os.makedirs(output_base_path, exist_ok=True)
    
    # EMG通道名称
    emg_channels = ['EMG1', 'EMG2', 'EMG3', 'EMG4', 'EMG5', 'EMG6', 'EMG7', 'EMG8']
    
    # 存储所有处理后的数据
    all_synergy_data = {}
    
    print("处理第四章三类人群协同模式...")
    
    # 获取所有目录 (格式: time0_healthy, time1_PDearly, time2_PDmid 等)
    for item in os.listdir(input_base_path):
        item_path = os.path.join(input_base_path, item)
        
        if not os.path.isdir(item_path):
            continue
        
        # 解析目录名称: time{Time}_{Group}
        parts = item.split('_')
        if len(parts) != 2:
            print(f"跳过格式不符的目录: {item}")
            continue
        
        time_part = parts[0]  # time0, time1, etc.
        group_part = parts[1]  # healthy, PDearly, PDmid
        
        # 提取时间阶段
        if time_part.startswith('time'):
            time_phase = time_part.replace('time', '')
        else:
            print(f"跳过格式不符的目录: {item}")
            continue
        
        # 映射组别名称
        group_mapping = {
            'healthy': 0,
            'PDearly': 1,
            'PDmid': 2
        }
        
        if group_part not in group_mapping:
            print(f"跳过未知组别: {group_part}")
            continue
        
        group_label = group_mapping[group_part]
        
        # 创建存储键
        key = f"time{time_phase}_{group_part}"
        all_synergy_data[key] = []
        
        print(f"\n处理 {item}...")
        
        # 获取所有子目录（个体文件）
        subdirs = [d for d in os.listdir(item_path) if os.path.isdir(os.path.join(item_path, d))]
        
        if not subdirs:
            print(f"  在 {item} 中未找到子目录")
            continue
        
        processed_count = 0
        
        for subdir in subdirs:
            subdir_path = os.path.join(item_path, subdir)
            
            # 查找 synergy_patterns.csv
            synergy_file = os.path.join(subdir_path, "synergy_patterns.csv")
            
            if not os.path.exists(synergy_file):
                print(f"  警告: 在 {subdir} 中未找到 synergy_patterns.csv")
                continue
            
            try:
                # 读取协同模式
                df = pd.read_csv(synergy_file, index_col=0)
                
                # 检查EMG列是否存在
                missing_cols = [col for col in emg_channels if col not in df.columns]
                if missing_cols:
                    print(f"  警告: {subdir} 中缺少列 {missing_cols}")
                    continue
                
                # 提取EMG数据
                emg_data = df[emg_channels].values
                
                # 计算L1范数（标准化前）
                l1_norms = np.sum(np.abs(emg_data), axis=1)
                
                # L2标准化每个协同向量
                normalized_data = []
                for i, row in enumerate(emg_data):
                    l2_norm = np.linalg.norm(row)
                    if l2_norm > 0:
                        normalized_row = row / l2_norm
                    else:
                        normalized_row = row  # 保持零向量为零
                    normalized_data.append(normalized_row)
                
                normalized_data = np.array(normalized_data)
                
                # 创建处理后的数据框
                processed_df = pd.DataFrame(normalized_data, columns=emg_channels)
                
                # 添加L1范数列
                processed_df['L1_Norm'] = l1_norms
                
                # 创建综合标签
                label = f"time{time_phase}_{group_part}"
                processed_df['Label'] = label
                
                # 添加详细信息
                processed_df['Time_Phase'] = time_phase
                processed_df['Group'] = group_part
                processed_df['Group_Label'] = group_label
                
                # 添加协同索引
                processed_df['Synergy_Index'] = [f'Synergy_{i+1}' for i in range(len(processed_df))]
                
                # 添加文件标识符
                processed_df['File_ID'] = subdir
                
                # 提取人名（从文件名中）
                person_name = extract_person_name_from_filename(subdir)
                processed_df['Person_Name'] = person_name
                
                # 存储处理后的数据
                all_synergy_data[key].append(processed_df)
                processed_count += 1
                
            except Exception as e:
                print(f"  处理 {subdir} 时出错: {str(e)}")
                continue
        
        print(f"  在 {item} 中处理了 {processed_count} 个文件")
    
    # 按不同分组保存结果
    save_results_by_time_group(all_synergy_data, output_base_path, emg_channels)
    save_results_by_group_only(all_synergy_data, output_base_path, emg_channels)
    save_combined_results(all_synergy_data, output_base_path, emg_channels)
    
    # 创建综合分析
    create_comprehensive_analysis(all_synergy_data, output_base_path, emg_channels)

def extract_person_name_from_filename(filename):
    """
    从文件名中提取人名（Cal前的部分）
    """
    import re
    
    # 查找"Cal"的位置
    cal_match = re.search(r'Cal', filename, re.IGNORECASE)
    if cal_match:
        # 提取Cal前的部分
        person_part = filename[:cal_match.start()].strip()
        # 移除可能的下划线或空格
        person_part = person_part.rstrip('_').strip()
        return person_part
    
    return filename  # 如果没有找到Cal，返回原文件名

def save_results_by_time_group(all_synergy_data, output_base_path, emg_channels):
    """
    按时间和组别保存结果（原始分组）
    """
    print("\n按时间和组别保存结果...")
    
    output_dir = os.path.join(output_base_path, "by_time_group")
    os.makedirs(output_dir, exist_ok=True)
    
    for key, data_list in all_synergy_data.items():
        if not data_list:
            continue
        
        # 合并该键的所有数据框
        combined_df = pd.concat(data_list, ignore_index=True)
        
        # 重新排列列
        column_order = emg_channels + ['L1_Norm', 'Label', 'Time_Phase', 'Group', 'Group_Label', 
                                      'Synergy_Index', 'File_ID', 'Person_Name']
        combined_df = combined_df[column_order]
        
        # 保存结果
        output_file = os.path.join(output_dir, f"{key}_synergy_patterns_processed.csv")
        combined_df.to_csv(output_file, index=False)
        
        print(f"  {key}: {len(combined_df)} 个协同向量，来自 {combined_df['File_ID'].nunique()} 个文件")
        
        # 创建汇总统计
        create_summary_statistics(combined_df, key, output_dir)

def save_results_by_group_only(all_synergy_data, output_base_path, emg_channels):
    """
    仅按组别保存结果（跨所有时间阶段）
    """
    print("\n仅按组别保存结果...")
    
    output_dir = os.path.join(output_base_path, "by_group_only")
    os.makedirs(output_dir, exist_ok=True)
    
    # 按组别分组
    group_data = {'healthy': [], 'PDearly': [], 'PDmid': []}
    
    for key, data_list in all_synergy_data.items():
        if not data_list:
            continue
        
        # 从键中提取组别
        group = key.split('_')[1]  # time0_healthy -> healthy
        
        if group in group_data:
            group_data[group].extend(data_list)
    
    # 保存合并结果
    for group, data_list in group_data.items():
        if not data_list:
            continue
        
        combined_df = pd.concat(data_list, ignore_index=True)
        
        # 重新排列列
        column_order = emg_channels + ['L1_Norm', 'Label', 'Time_Phase', 'Group', 'Group_Label', 
                                      'Synergy_Index', 'File_ID', 'Person_Name']
        combined_df = combined_df[column_order]
        
        # 保存结果
        output_file = os.path.join(output_dir, f"{group}_synergy_patterns_processed.csv")
        combined_df.to_csv(output_file, index=False)
        
        print(f"  {group}: {len(combined_df)} 个协同向量，来自 {combined_df['File_ID'].nunique()} 个文件")
        print(f"    时间阶段: {sorted(combined_df['Time_Phase'].unique())}")
        
        # 创建汇总统计
        create_summary_statistics(combined_df, group, output_dir)

def save_combined_results(all_synergy_data, output_base_path, emg_channels):
    """
    保存所有合并结果
    """
    print("\n保存所有合并结果...")
    
    # 合并所有数据
    all_data = []
    for key, data_list in all_synergy_data.items():
        if data_list:
            all_data.extend(data_list)
    
    if not all_data:
        print("  没有数据可合并")
        return
    
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # 重新排列列
    column_order = emg_channels + ['L1_Norm', 'Label', 'Time_Phase', 'Group', 'Group_Label', 
                                  'Synergy_Index', 'File_ID', 'Person_Name']
    combined_df = combined_df[column_order]
    
    # 保存合并结果
    output_file = os.path.join(output_base_path, "all_synergy_patterns_processed.csv")
    combined_df.to_csv(output_file, index=False)
    
    print(f"  全部合并: {len(combined_df)} 个协同向量，来自 {combined_df['File_ID'].nunique()} 个文件")
    print(f"    组别: {sorted(combined_df['Group'].unique())}")
    print(f"    时间阶段: {sorted(combined_df['Time_Phase'].unique())}")
    
    # 创建汇总统计
    create_summary_statistics(combined_df, "all_combined", output_base_path)

def create_summary_statistics(df, label, output_path):
    """
    创建处理后协同数据的汇总统计
    """
    summary_file = os.path.join(output_path, f"{label}_summary_statistics.txt")

    with open(summary_file, 'w', encoding='utf-8') as f:
        f.write(f"=== {label} 协同模式汇总 ===\n\n")

        # 基本统计
        f.write(f"协同向量总数: {len(df)}\n")
        f.write(f"唯一文件数: {df['File_ID'].nunique()}\n")
        f.write(f"唯一标签数: {df['Label'].nunique()}\n\n")

        # 组别分布
        if 'Group' in df.columns:
            f.write("组别分布:\n")
            group_counts = df['Group'].value_counts().sort_index()
            for group, count in group_counts.items():
                f.write(f"  {group}: {count} 个协同\n")
            f.write("\n")

        # 时间阶段分布
        if 'Time_Phase' in df.columns:
            f.write("时间阶段分布:\n")
            time_counts = df['Time_Phase'].value_counts().sort_index()
            for time_phase, count in time_counts.items():
                f.write(f"  时间 {time_phase}: {count} 个协同\n")
            f.write("\n")

        # 标签分布
        f.write("标签分布:\n")
        label_counts = df['Label'].value_counts().sort_index()
        for label_name, count in label_counts.items():
            f.write(f"  {label_name}: {count} 个协同\n")
        f.write("\n")

        # 每个文件的协同数
        f.write("每个文件的协同数:\n")
        file_counts = df['File_ID'].value_counts()
        f.write(f"  均值: {file_counts.mean():.2f}\n")
        f.write(f"  标准差: {file_counts.std():.2f}\n")
        f.write(f"  范围: {file_counts.min()}-{file_counts.max()}\n\n")

        # L1范数统计
        f.write("L1范数统计:\n")
        f.write(f"  均值: {df['L1_Norm'].mean():.4f}\n")
        f.write(f"  标准差: {df['L1_Norm'].std():.4f}\n")
        f.write(f"  范围: {df['L1_Norm'].min():.4f}-{df['L1_Norm'].max():.4f}\n\n")

        # EMG通道统计（标准化值）
        emg_channels = ['EMG1', 'EMG2', 'EMG3', 'EMG4', 'EMG5', 'EMG6', 'EMG7', 'EMG8']
        f.write("标准化EMG通道统计:\n")
        for channel in emg_channels:
            if channel in df.columns:
                mean_val = df[channel].mean()
                std_val = df[channel].std()
                f.write(f"  {channel}: {mean_val:.4f} ± {std_val:.4f}\n")

def create_comprehensive_analysis(all_synergy_data, output_base_path, emg_channels):
    """
    创建综合分析和可视化
    """
    print("\n创建综合分析...")

    # 合并所有数据用于分析
    all_data = []
    for key, data_list in all_synergy_data.items():
        if data_list:
            all_data.extend(data_list)

    if not all_data:
        print("  没有数据用于综合分析")
        return

    combined_df = pd.concat(all_data, ignore_index=True)

    # 创建分析目录
    analysis_dir = os.path.join(output_base_path, "comprehensive_analysis")
    os.makedirs(analysis_dir, exist_ok=True)

    # 1. 组别对比分析
    create_group_comparison_analysis(combined_df, analysis_dir, emg_channels)

    # 2. 时间阶段分析
    create_time_phase_analysis(combined_df, analysis_dir, emg_channels)

    # 3. 创建可视化
    create_comprehensive_visualizations(combined_df, analysis_dir, emg_channels)

def create_group_comparison_analysis(df, output_dir, emg_channels):
    """
    创建组别对比分析（三类人群对比）
    """
    print("  创建组别对比分析...")

    group_analysis = {}

    for group in df['Group'].unique():
        group_data = df[df['Group'] == group]

        group_stats = {}
        for channel in emg_channels:
            group_stats[channel] = {
                'mean': group_data[channel].mean(),
                'std': group_data[channel].std(),
                'median': group_data[channel].median()
            }

        group_stats['L1_Norm'] = {
            'mean': group_data['L1_Norm'].mean(),
            'std': group_data['L1_Norm'].std(),
            'median': group_data['L1_Norm'].median()
        }

        group_analysis[group] = {
            'stats': group_stats,
            'count': len(group_data),
            'files': group_data['File_ID'].nunique(),
            'persons': group_data['Person_Name'].nunique()
        }

    # 保存组别对比
    comparison_file = os.path.join(output_dir, "group_comparison_analysis.txt")
    with open(comparison_file, 'w', encoding='utf-8') as f:
        f.write("=== 三类人群对比分析 ===\n\n")

        for group, analysis in group_analysis.items():
            group_names = {'healthy': '健康人群', 'PDearly': '早期PD', 'PDmid': '中期PD'}
            group_name_cn = group_names.get(group, group)

            f.write(f"{group_name_cn} ({group}):\n")
            f.write(f"  协同总数: {analysis['count']}\n")
            f.write(f"  唯一文件数: {analysis['files']}\n")
            f.write(f"  唯一人数: {analysis['persons']}\n")
            f.write(f"  L1范数: {analysis['stats']['L1_Norm']['mean']:.4f} ± {analysis['stats']['L1_Norm']['std']:.4f}\n")
            f.write("\n")

            f.write("  EMG通道统计:\n")
            for channel in emg_channels:
                stats = analysis['stats'][channel]
                f.write(f"    {channel}: {stats['mean']:.4f} ± {stats['std']:.4f}\n")
            f.write("\n")

def create_time_phase_analysis(df, output_dir, emg_channels):
    """
    创建时间阶段分析
    """
    print("  创建时间阶段分析...")

    time_analysis = {}

    for time_phase in sorted(df['Time_Phase'].unique()):
        time_data = df[df['Time_Phase'] == time_phase]

        time_stats = {}
        for channel in emg_channels:
            time_stats[channel] = {
                'mean': time_data[channel].mean(),
                'std': time_data[channel].std()
            }

        time_analysis[time_phase] = {
            'stats': time_stats,
            'count': len(time_data),
            'groups': time_data['Group'].value_counts().to_dict()
        }

    # 保存时间阶段分析
    time_file = os.path.join(output_dir, "time_phase_analysis.txt")
    with open(time_file, 'w', encoding='utf-8') as f:
        f.write("=== 时间阶段分析 ===\n\n")

        for time_phase, analysis in time_analysis.items():
            f.write(f"时间阶段 {time_phase}:\n")
            f.write(f"  协同总数: {analysis['count']}\n")
            f.write(f"  组别分布: {analysis['groups']}\n")
            f.write("\n")

def create_comprehensive_visualizations(df, output_dir, emg_channels):
    """
    创建综合可视化
    """
    print("  创建综合可视化...")

    # 设置样式
    plt.style.use('default')
    plt.rcParams['font.size'] = 10

    # 1. 三组对比热图
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    groups = ['healthy', 'PDearly', 'PDmid']
    group_names = {'healthy': '健康人群', 'PDearly': '早期PD', 'PDmid': '中期PD'}

    for i, group in enumerate(groups):
        if group in df['Group'].values:
            group_data = df[df['Group'] == group]
            group_means = group_data[emg_channels].mean()

            ax = axes[i]
            sns.heatmap(group_means.values.reshape(1, -1),
                       xticklabels=emg_channels,
                       yticklabels=[group_names[group]],
                       annot=True, fmt='.3f', cmap='viridis', ax=ax)
            ax.set_title(f'{group_names[group]} 平均EMG模式')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "three_group_comparison_heatmap.png"), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 时间阶段演化
    fig, ax = plt.subplots(figsize=(12, 8))

    time_means = df.groupby(['Time_Phase', 'Group'])[emg_channels].mean().reset_index()

    colors = {'healthy': 'skyblue', 'PDearly': 'lightgreen', 'PDmid': 'lightcoral'}

    for group in groups:
        if group in df['Group'].values:
            group_time_data = time_means[time_means['Group'] == group]
            for channel in emg_channels:
                ax.plot(group_time_data['Time_Phase'], group_time_data[channel],
                       label=f'{group_names[group]} {channel}',
                       color=colors[group], alpha=0.7)

    ax.set_xlabel('时间阶段')
    ax.set_ylabel('平均标准化EMG')
    ax.set_title('三类人群EMG模式在时间阶段中的演化')
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "time_phase_evolution.png"), dpi=300, bbox_inches='tight')
    plt.close()

    print("  综合分析完成!")

def verify_normalization_by_groups(output_base_path, emg_channels):
    """
    验证所有组别的协同向量是否正确标准化
    """
    print("\n验证所有组别的标准化...")

    # 检查合并结果文件
    combined_file = os.path.join(output_base_path, "all_synergy_patterns_processed.csv")

    if not os.path.exists(combined_file):
        print("  未找到合并结果文件")
        return

    df = pd.read_csv(combined_file)

    # 计算标准化向量的L2范数
    emg_data = df[emg_channels].values
    l2_norms = np.linalg.norm(emg_data, axis=1)

    print(f"标准化后的L2范数:")
    print(f"  均值: {l2_norms.mean():.6f}")
    print(f"  标准差: {l2_norms.std():.6f}")
    print(f"  范围: {l2_norms.min():.6f}-{l2_norms.max():.6f}")

    # 检查是否所有范数都约等于1
    tolerance = 1e-10
    properly_normalized = np.all(np.abs(l2_norms - 1.0) < tolerance)
    print(f"  所有向量正确标准化 (L2=1): {properly_normalized}")

    # 按组别检查
    for group in df['Group'].unique():
        group_data = df[df['Group'] == group]
        group_emg_data = group_data[emg_channels].values
        group_l2_norms = np.linalg.norm(group_emg_data, axis=1)

        group_names = {'healthy': '健康人群', 'PDearly': '早期PD', 'PDmid': '中期PD'}
        group_name_cn = group_names.get(group, group)

        print(f"\n{group_name_cn} ({group}):")
        print(f"  L2范数 - 均值: {group_l2_norms.mean():.6f}, 标准差: {group_l2_norms.std():.6f}")
        print(f"  正确标准化: {np.all(np.abs(group_l2_norms - 1.0) < tolerance)}")

if __name__ == "__main__":
    normalize_and_calculate_l1_norm_by_groups()

    # 验证结果
    output_base_path = r"D:\论文\中期\第四章\数据\模式\synergy_patterns_processed_by_groups"
    emg_channels = ['EMG1', 'EMG2', 'EMG3', 'EMG4', 'EMG5', 'EMG6', 'EMG7', 'EMG8']

    verify_normalization_by_groups(output_base_path, emg_channels)

    print("\n处理完成!")
    print(f"结果保存到: {output_base_path}")
    print("\n生成的目录:")
    print("- by_time_group/: 按时间和组别分组 (time0_healthy, time1_PDearly 等)")
    print("- by_group_only/: 仅按组别分组 (healthy, PDearly, PDmid)")
    print("- comprehensive_analysis/: 跨组分析和可视化")
    print("- all_synergy_patterns_processed.csv: 所有数据合并")
