import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import normalize
from sklearn.metrics import silhouette_score, davies_bouldin_score
from sklearn.cluster import KMeans
from sklearn.decomposition import PCA
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# Configuration
INPUT_DATA_PATH = r"D:\论文\中期\第四章\数据\模式\synergy_patterns_processed_by_groups\all_synergy_patterns_processed.csv"
OUTPUT_BASE_DIRECTORY = r"D:\论文\中期\第四章\数据\模式\phase_separated_clustering_results"
K_RANGE = (2, 11)
PERFORM_STABILITY_ANALYSIS = True
RANDOM_STATE = 42

class SphericalKMeans:
    """
    自定义球面K-Means聚类，使用余弦距离
    """
    def __init__(self, n_clusters, max_iter=300, tol=1e-4, random_state=None):
        self.n_clusters = n_clusters
        self.max_iter = max_iter
        self.tol = tol
        self.random_state = random_state
        self.cluster_centers_ = None
        self.labels_ = None
        self.inertia_ = None
        
    def fit(self, X):
        """
        拟合球面k-means到数据
        """
        if self.random_state:
            np.random.seed(self.random_state)
        
        # 标准化数据到单位球面
        X_normalized = normalize(X, norm='l2')
        n_samples, n_features = X_normalized.shape
        
        # 在单位球面上随机初始化质心
        centroids = np.random.randn(self.n_clusters, n_features)
        centroids = normalize(centroids, norm='l2')
        
        for iteration in range(self.max_iter):
            # 使用余弦相似度将点分配给最近的质心
            similarities = np.dot(X_normalized, centroids.T)
            labels = np.argmax(similarities, axis=1)
            
            # 更新质心
            new_centroids = np.zeros_like(centroids)
            for k in range(self.n_clusters):
                mask = labels == k
                if np.sum(mask) > 0:
                    # 分配点的均值，然后标准化
                    new_centroids[k] = np.mean(X_normalized[mask], axis=0)
                    new_centroids[k] = normalize(new_centroids[k].reshape(1, -1), norm='l2').flatten()
                else:
                    # 重新初始化空簇
                    new_centroids[k] = normalize(np.random.randn(1, n_features), norm='l2').flatten()
            
            # 检查收敛性
            centroid_shift = np.mean([1 - np.dot(centroids[k], new_centroids[k]) 
                                    for k in range(self.n_clusters)])
            
            centroids = new_centroids
            
            if centroid_shift < self.tol:
                break
        
        # 计算惯性（到质心的余弦距离之和）
        inertia = 0
        for i in range(n_samples):
            centroid_idx = labels[i]
            cosine_dist = 1 - np.dot(X_normalized[i], centroids[centroid_idx])
            inertia += cosine_dist
        
        self.cluster_centers_ = centroids
        self.labels_ = labels
        self.inertia_ = inertia
        
        return self
    
    def fit_predict(self, X):
        """
        拟合并预测聚类标签
        """
        self.fit(X)
        return self.labels_
    
    def predict(self, X):
        """
        为新数据预测聚类标签
        """
        X_normalized = normalize(X, norm='l2')
        similarities = np.dot(X_normalized, self.cluster_centers_.T)
        return np.argmax(similarities, axis=1)

def load_synergy_data():
    """
    加载处理后的协同数据
    """
    print("加载协同数据...")
    
    if not os.path.exists(INPUT_DATA_PATH):
        print(f"数据文件未找到: {INPUT_DATA_PATH}")
        return None
    
    df = pd.read_csv(INPUT_DATA_PATH)
    print(f"加载数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 检查必需的列
    emg_columns = [f'EMG{i}' for i in range(1, 9)]
    required_columns = emg_columns + ['Time_Phase', 'Group', 'Group_Label', 'File_ID']
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"缺少必需的列: {missing_columns}")
        return None
    
    print(f"可用时间阶段: {sorted(df['Time_Phase'].unique())}")
    print(f"可用组别: {sorted(df['Group'].unique())}")
    print(f"组别标签分布: {df['Group_Label'].value_counts().sort_index().to_dict()}")
    
    return df

def extract_phase_data(df, time_phase):
    """
    提取特定时间阶段的数据
    """
    # 过滤数据
    phase_data = df[df['Time_Phase'] == time_phase].copy()
    
    if len(phase_data) == 0:
        return None, None
    
    # 提取EMG数据
    emg_columns = [f'EMG{i}' for i in range(1, 9)]
    X = phase_data[emg_columns].values
    
    # 创建文件信息数据框
    file_info_df = phase_data[['Time_Phase', 'Group', 'Group_Label', 'File_ID', 'Synergy_Index', 'Person_Name']].copy()
    
    # 添加人群标签 (0: healthy, 1: PDearly, 2: PDmid)
    file_info_df['Population_Label'] = file_info_df['Group_Label']
    
    print(f"时间阶段 {time_phase}: {len(X)} 个样本")
    print(f"  健康人群: {np.sum(file_info_df['Population_Label'] == 0)} 个样本")
    print(f"  早期PD: {np.sum(file_info_df['Population_Label'] == 1)} 个样本")
    print(f"  中期PD: {np.sum(file_info_df['Population_Label'] == 2)} 个样本")
    
    return X, file_info_df

def optimize_clustering(X, k_range=(2, 11), random_state=42):
    """
    使用多个指标优化聚类参数
    """
    print("优化聚类参数...")
    
    # 为球面聚类标准化数据
    X_normalized = normalize(X, norm='l2')
    
    k_values = list(range(k_range[0], k_range[1]))
    silhouette_scores = []
    db_indices = []
    inertias = []
    
    for k in tqdm(k_values, desc="测试k值"):
        # 自定义球面K-means
        spherical_kmeans = SphericalKMeans(n_clusters=k, random_state=random_state)
        labels = spherical_kmeans.fit_predict(X_normalized)
        
        # 使用余弦距离计算球面数据的指标
        sil_score = silhouette_score(X_normalized, labels, metric='cosine')
        db_score = davies_bouldin_score(X_normalized, labels)
        
        silhouette_scores.append(sil_score)
        db_indices.append(db_score)
        inertias.append(spherical_kmeans.inertia_)
    
    # 找到最优k
    optimal_k_idx = np.argmax(silhouette_scores)
    optimal_k = k_values[optimal_k_idx]
    
    results = {
        'k_values': k_values,
        'silhouette_scores': silhouette_scores,
        'db_indices': db_indices,
        'inertias': inertias,
        'optimal_k': optimal_k,
        'optimal_k_idx': optimal_k_idx
    }
    
    print(f"最优k: {optimal_k} (轮廓系数: {silhouette_scores[optimal_k_idx]:.4f})")
    
    return results

def plot_optimization_results(optimization_results, output_dir, phase_name):
    """
    绘制聚类优化结果
    """
    print("绘制优化结果...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    k_values = optimization_results['k_values']
    optimal_k = optimization_results['optimal_k']
    
    # 轮廓系数
    axes[0, 0].plot(k_values, optimization_results['silhouette_scores'], 'bo-')
    axes[0, 0].axvline(x=optimal_k, color='red', linestyle='--', alpha=0.7)
    axes[0, 0].set_xlabel('聚类数 (k)')
    axes[0, 0].set_ylabel('轮廓系数')
    axes[0, 0].set_title('轮廓系数 vs k')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Davies-Bouldin指数
    axes[0, 1].plot(k_values, optimization_results['db_indices'], 'ro-')
    axes[0, 1].axvline(x=optimal_k, color='red', linestyle='--', alpha=0.7)
    axes[0, 1].set_xlabel('聚类数 (k)')
    axes[0, 1].set_ylabel('Davies-Bouldin指数')
    axes[0, 1].set_title('Davies-Bouldin指数 vs k (越小越好)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 惯性（肘部法）
    axes[1, 0].plot(k_values, optimization_results['inertias'], 'go-')
    axes[1, 0].axvline(x=optimal_k, color='red', linestyle='--', alpha=0.7)
    axes[1, 0].set_xlabel('聚类数 (k)')
    axes[1, 0].set_ylabel('惯性')
    axes[1, 0].set_title('惯性 vs k (肘部法)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 组合指标
    # 标准化指标进行比较
    sil_norm = np.array(optimization_results['silhouette_scores'])
    db_norm = 1 / (1 + np.array(optimization_results['db_indices']))  # 反转DB指数
    
    axes[1, 1].plot(k_values, sil_norm, 'b-', label='轮廓系数 (标准化)', alpha=0.7)
    axes[1, 1].plot(k_values, db_norm, 'r-', label='DB指数 (反转&标准化)', alpha=0.7)
    axes[1, 1].axvline(x=optimal_k, color='red', linestyle='--', alpha=0.7)
    axes[1, 1].set_xlabel('聚类数 (k)')
    axes[1, 1].set_ylabel('标准化分数')
    axes[1, 1].set_title('组合指标')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.suptitle(f'聚类优化结果 - {phase_name}', fontsize=16)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "clustering_optimization.png"), dpi=300, bbox_inches='tight')
    plt.close()

if __name__ == "__main__":
    print("=== 三类人群分阶段球面聚类分析 ===")
    print(f"输入数据: {INPUT_DATA_PATH}")
    print(f"输出目录: {OUTPUT_BASE_DIRECTORY}")
    print(f"K范围: {K_RANGE}")
    print(f"执行稳定性分析: {PERFORM_STABILITY_ANALYSIS}")
    
    # 创建输出目录
    os.makedirs(OUTPUT_BASE_DIRECTORY, exist_ok=True)
    
    # 加载数据
    df = load_synergy_data()
    if df is None:
        print("加载数据失败。退出。")
        exit()
    
    print("\n数据加载成功！")

def bootstrap_stability_analysis(X, optimal_k, n_bootstrap=100, sample_ratio=0.8, random_state=42):
    """
    执行自助法稳定性分析
    """
    print(f"执行自助法稳定性分析 (n_bootstrap={n_bootstrap})...")

    np.random.seed(random_state)
    X_normalized = normalize(X, norm='l2')
    n_samples = len(X)
    sample_size = int(n_samples * sample_ratio)

    # 在完整数据集上的参考聚类
    ref_kmeans = SphericalKMeans(n_clusters=optimal_k, random_state=random_state)
    ref_labels = ref_kmeans.fit_predict(X_normalized)

    stability_scores = []

    for i in tqdm(range(n_bootstrap), desc="自助法迭代"):
        # 自助法采样
        bootstrap_indices = np.random.choice(n_samples, size=sample_size, replace=True)
        X_bootstrap = X_normalized[bootstrap_indices]

        # 对自助法样本聚类
        bootstrap_kmeans = SphericalKMeans(n_clusters=optimal_k, random_state=random_state + i)
        bootstrap_labels = bootstrap_kmeans.fit_predict(X_bootstrap)

        # 计算稳定性（自助法样本上的轮廓系数）
        if len(np.unique(bootstrap_labels)) > 1:
            stability_score = silhouette_score(X_bootstrap, bootstrap_labels, metric='cosine')
            stability_scores.append(stability_score)

    results = {
        'stability_scores': stability_scores,
        'mean_stability': np.mean(stability_scores),
        'std_stability': np.std(stability_scores),
        'overall_stability': np.mean(stability_scores),
        'n_bootstrap': n_bootstrap,
        'sample_ratio': sample_ratio
    }

    print(f"自助法稳定性: {results['mean_stability']:.4f} ± {results['std_stability']:.4f}")

    return results

def plot_stability_analysis(stability_results, final_labels, output_dir, phase_name):
    """
    绘制稳定性分析结果
    """
    print("绘制稳定性分析...")

    fig, axes = plt.subplots(1, 2, figsize=(15, 6))

    # 稳定性分数分布
    stability_scores = stability_results['stability_scores']
    axes[0].hist(stability_scores, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0].axvline(x=stability_results['mean_stability'], color='red', linestyle='--',
                   label=f'均值: {stability_results["mean_stability"]:.4f}')
    axes[0].set_xlabel('稳定性分数')
    axes[0].set_ylabel('频率')
    axes[0].set_title('自助法稳定性分数分布')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # 聚类大小分布
    unique_labels, label_counts = np.unique(final_labels, return_counts=True)
    axes[1].bar(unique_labels, label_counts, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1].set_xlabel('聚类ID')
    axes[1].set_ylabel('样本数量')
    axes[1].set_title('最终聚类大小分布')
    axes[1].grid(True, alpha=0.3)

    plt.suptitle(f'稳定性分析 - {phase_name}', fontsize=16)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "stability_analysis.png"), dpi=300, bbox_inches='tight')
    plt.close()

def plot_clustering_results(X, labels, file_info_df, output_dir, phase_name):
    """
    绘制聚类结果和组别信息
    """
    print("绘制聚类结果...")

    # 标准化数据用于可视化
    X_normalized = normalize(X, norm='l2')

    # PCA用于2D可视化
    pca = PCA(n_components=2, random_state=42)
    X_pca = pca.fit_transform(X_normalized)

    fig, axes = plt.subplots(1, 2, figsize=(16, 6))

    # 按聚类标签绘制
    unique_labels = np.unique(labels)
    colors = plt.cm.Set3(np.linspace(0, 1, len(unique_labels)))

    for i, label in enumerate(unique_labels):
        mask = labels == label
        axes[0].scatter(X_pca[mask, 0], X_pca[mask, 1],
                       c=[colors[i]], label=f'聚类 {label}', alpha=0.7, s=50)

    axes[0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} 方差)')
    axes[0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} 方差)')
    axes[0].set_title('聚类结果 (按聚类)')
    axes[0].legend()
    axes[0].grid(True, alpha=0.3)

    # 按人群组别绘制
    population_labels = file_info_df['Population_Label'].values
    group_colors = ['skyblue', 'lightgreen', 'lightcoral']  # 健康、早期PD、中期PD
    group_names = ['健康人群', '早期PD', '中期PD']

    for pop_label in [0, 1, 2]:
        mask = population_labels == pop_label
        if np.any(mask):
            axes[1].scatter(X_pca[mask, 0], X_pca[mask, 1],
                           c=group_colors[pop_label], label=group_names[pop_label],
                           alpha=0.7, s=50)

    axes[1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} 方差)')
    axes[1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} 方差)')
    axes[1].set_title('数据分布 (按人群组别)')
    axes[1].legend()
    axes[1].grid(True, alpha=0.3)

    plt.suptitle(f'聚类可视化 - {phase_name}', fontsize=16)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "clustering_visualization.png"), dpi=300, bbox_inches='tight')
    plt.close()

def save_clustering_results(X, labels, file_info_df, optimization_results, stability_results, output_dir, phase_name):
    """
    保存所有聚类结果到CSV文件
    """
    print("保存聚类结果...")

    emg_columns = [f'EMG{i}' for i in range(1, X.shape[1] + 1)]

    # 主要结果数据框
    results_df = file_info_df.copy()
    results_df['Cluster_Label'] = labels

    # 添加EMG数据和标准化EMG数据
    X_normalized = normalize(X, norm='l2')
    emg_df = pd.DataFrame(X, columns=emg_columns)
    emg_norm_df = pd.DataFrame(X_normalized, columns=[f'{col}_norm' for col in emg_columns])

    results_df = pd.concat([results_df, emg_df, emg_norm_df], axis=1)

    # 保存主要结果
    results_df.to_csv(os.path.join(output_dir, "final_clustering_results.csv"), index=False)

    # 保存优化结果
    opt_df = pd.DataFrame({
        'k': optimization_results['k_values'],
        'silhouette_score': optimization_results['silhouette_scores'],
        'db_index': optimization_results['db_indices'],
        'inertia': optimization_results['inertias']
    })
    opt_df.to_csv(os.path.join(output_dir, "clustering_optimization_metrics.csv"), index=False)

    # 保存聚类质心
    spherical_kmeans = SphericalKMeans(n_clusters=optimization_results['optimal_k'], random_state=42)
    spherical_kmeans.fit(normalize(X, norm='l2'))
    centroids_df = pd.DataFrame(spherical_kmeans.cluster_centers_, columns=emg_columns)
    centroids_df['Cluster_ID'] = range(len(centroids_df))
    centroids_df['N_Samples'] = pd.Series(labels).value_counts().sort_index().values
    centroids_df.to_csv(os.path.join(output_dir, "cluster_centroids.csv"), index=False)

    # 保存分析摘要
    population_labels = file_info_df['Population_Label'].values
    summary = {
        'phase_name': phase_name,
        'optimal_k': optimization_results['optimal_k'],
        'silhouette_score': optimization_results['silhouette_scores'][optimization_results['optimal_k_idx']],
        'db_index': optimization_results['db_indices'][optimization_results['optimal_k_idx']],
        'n_samples': len(labels),
        'n_healthy': np.sum(population_labels == 0),
        'n_PDearly': np.sum(population_labels == 1),
        'n_PDmid': np.sum(population_labels == 2)
    }

    if stability_results is not None:
        summary['overall_stability'] = stability_results['overall_stability']
        summary['n_bootstrap_iterations'] = stability_results['n_bootstrap']
        summary['bootstrap_sample_ratio'] = stability_results['sample_ratio']

    pd.DataFrame([summary]).to_csv(os.path.join(output_dir, "analysis_summary.csv"), index=False)

    print("所有结果保存成功。")

def process_single_phase(df, time_phase, output_base_dir, k_range=(2, 11),
                        perform_stability=True, random_state=42):
    """
    处理单个时间阶段
    """
    phase_name = f"time{time_phase}"

    print(f"\n{'='*60}")
    print(f"处理阶段: {phase_name}")
    print(f"{'='*60}")

    # 为此阶段创建输出目录
    phase_output_dir = os.path.join(output_base_dir, phase_name)
    os.makedirs(phase_output_dir, exist_ok=True)

    # 提取阶段数据
    print(f"步骤 1: 提取 {phase_name} 的数据...")
    X, file_info_df = extract_phase_data(df, time_phase)

    if X is None or len(X) == 0:
        print(f"未找到 {phase_name} 的数据。跳过。")
        return None

    # 检查最小样本要求
    if len(X) < 10:
        print(f"{phase_name} 样本不足 ({len(X)})。跳过。")
        return None

    # 优化聚类参数
    print(f"步骤 2: 优化聚类参数...")
    optimization_results = optimize_clustering(X, k_range, random_state)
    optimal_k = optimization_results['optimal_k']

    # 绘制优化结果
    print(f"步骤 3: 绘制优化结果...")
    plot_optimization_results(optimization_results, phase_output_dir, phase_name)

    # 使用最优参数进行最终聚类
    print(f"步骤 4: 使用 k={optimal_k} 进行最终聚类...")
    X_normalized = normalize(X, norm='l2')
    final_spherical_kmeans = SphericalKMeans(n_clusters=optimal_k, random_state=random_state)
    final_labels = final_spherical_kmeans.fit_predict(X_normalized)

    # 绘制聚类结果
    print(f"步骤 5: 绘制聚类结果...")
    plot_clustering_results(X, final_labels, file_info_df, phase_output_dir, phase_name)

    # 自助法稳定性分析
    stability_results = None
    if perform_stability and len(X) >= 20:  # 只有在样本足够时
        print(f"步骤 6: 执行自助法稳定性分析...")
        stability_results = bootstrap_stability_analysis(X, optimal_k, n_bootstrap=100, random_state=random_state)

        # 绘制稳定性分析
        print(f"步骤 7: 绘制稳定性分析结果...")
        plot_stability_analysis(stability_results, final_labels, phase_output_dir, phase_name)
    else:
        print(f"步骤 6-7: 跳过稳定性分析 (样本不足或已禁用)")

    # 保存所有结果
    print(f"步骤 8: 保存最终结果...")
    save_clustering_results(X, final_labels, file_info_df, optimization_results,
                           stability_results, phase_output_dir, phase_name)

    # 此阶段的摘要
    phase_summary = {
        'phase_name': phase_name,
        'time_phase': time_phase,
        'n_samples': len(X),
        'n_healthy': np.sum(file_info_df['Population_Label'] == 0),
        'n_PDearly': np.sum(file_info_df['Population_Label'] == 1),
        'n_PDmid': np.sum(file_info_df['Population_Label'] == 2),
        'optimal_k': optimal_k,
        'silhouette_score': optimization_results['silhouette_scores'][optimization_results['optimal_k_idx']],
        'db_index': optimization_results['db_indices'][optimization_results['optimal_k_idx']],
        'stability_score': stability_results['overall_stability'] if stability_results else None,
        'output_dir': phase_output_dir
    }

    print(f"\n阶段 {phase_name} 完成成功!")
    print(f"结果保存到: {phase_output_dir}")

    return phase_summary

def main():
    """
    主函数处理所有阶段
    """
    print("=== 三类人群分阶段球面聚类分析 ===")
    print(f"输入数据: {INPUT_DATA_PATH}")
    print(f"输出目录: {OUTPUT_BASE_DIRECTORY}")
    print(f"K范围: {K_RANGE}")
    print(f"执行稳定性分析: {PERFORM_STABILITY_ANALYSIS}")

    # 创建输出目录
    os.makedirs(OUTPUT_BASE_DIRECTORY, exist_ok=True)

    # 加载数据
    df = load_synergy_data()
    if df is None:
        print("加载数据失败。退出。")
        return

    # 获取所有唯一的时间阶段
    time_phases = sorted(df['Time_Phase'].unique())

    print(f"\n处理 {len(time_phases)} 个时间阶段")
    print(f"时间阶段: {time_phases}")

    # 处理每个时间阶段
    all_phase_summaries = []
    successful_phases = 0
    failed_phases = 0

    for time_phase in time_phases:
        try:
            print(f"\n[{successful_phases + failed_phases + 1}/{len(time_phases)}] 处理: time{time_phase}")

            phase_summary = process_single_phase(
                df, time_phase,
                OUTPUT_BASE_DIRECTORY,
                k_range=K_RANGE,
                perform_stability=PERFORM_STABILITY_ANALYSIS,
                random_state=RANDOM_STATE
            )

            if phase_summary is not None:
                all_phase_summaries.append(phase_summary)
                successful_phases += 1
            else:
                failed_phases += 1

        except Exception as e:
            print(f"处理 time{time_phase} 时出错: {e}")
            failed_phases += 1
            continue

    # 保存总体摘要
    if all_phase_summaries:
        print(f"\n{'='*60}")
        print("保存总体摘要...")

        summary_df = pd.DataFrame(all_phase_summaries)
        summary_file = os.path.join(OUTPUT_BASE_DIRECTORY, "all_phases_summary.csv")
        summary_df.to_csv(summary_file, index=False)

        # 创建详细摘要报告
        report_file = os.path.join(OUTPUT_BASE_DIRECTORY, "analysis_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== 三类人群分阶段球面聚类分析报告 ===\n\n")
            f.write(f"处理的总阶段数: {len(time_phases)}\n")
            f.write(f"成功: {successful_phases}\n")
            f.write(f"失败: {failed_phases}\n\n")

            f.write("阶段结果:\n")
            f.write("-" * 120 + "\n")
            f.write(f"{'阶段':<10} {'时间':<5} {'样本':<6} {'健康/早期/中期':<15} {'最优K':<6} {'轮廓系数':<10} {'DB指数':<8} {'稳定性':<8}\n")
            f.write("-" * 120 + "\n")

            for summary in all_phase_summaries:
                stability_str = f"{summary['stability_score']:.4f}" if summary['stability_score'] else "N/A"
                group_str = f"{summary['n_healthy']}/{summary['n_PDearly']}/{summary['n_PDmid']}"
                f.write(f"{summary['phase_name']:<10} {summary['time_phase']:<5} "
                       f"{summary['n_samples']:<6} {group_str:<15} {summary['optimal_k']:<6} "
                       f"{summary['silhouette_score']:<10.4f} {summary['db_index']:<8.4f} {stability_str:<8}\n")

            f.write("\n" + "=" * 120 + "\n")
            f.write("分析完成成功!\n")
            f.write(f"所有结果保存到: {OUTPUT_BASE_DIRECTORY}\n")

        print(f"总体摘要保存到: {summary_file}")
        print(f"详细报告保存到: {report_file}")

    print(f"\n{'='*60}")
    print("=== 分析完成 ===")
    print(f"成功处理 {successful_phases}/{len(time_phases)} 个阶段")
    print(f"结果保存到: {OUTPUT_BASE_DIRECTORY}")

    if all_phase_summaries:
        print(f"\n表现最佳的阶段 (按轮廓系数):")
        sorted_summaries = sorted(all_phase_summaries, key=lambda x: x['silhouette_score'], reverse=True)
        for i, summary in enumerate(sorted_summaries[:5], 1):
            print(f"  {i}. {summary['phase_name']}: 轮廓系数={summary['silhouette_score']:.4f}, k={summary['optimal_k']}, 样本={summary['n_samples']}")

if __name__ == "__main__":
    main()
