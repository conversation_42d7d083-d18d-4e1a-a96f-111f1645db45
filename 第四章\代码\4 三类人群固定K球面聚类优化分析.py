import pandas as pd
import numpy as np
import os
import glob
from pathlib import Path
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import normalize
from sklearn.metrics import silhouette_score, davies_bouldin_score
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score
from scipy.optimize import linear_sum_assignment
from collections import defaultdict
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# Configuration
INPUT_DATA_PATH = r"D:\论文\中期\第四章\数据\模式\synergy_patterns_processed_by_groups\all_synergy_patterns_processed.csv"
OUTPUT_BASE_DIR = r"D:\论文\中期\第四章\数据\模式\phase_separated_clustering_results_fixed_k_optimized"
FIXED_K = 4  # 固定聚类数

class SphericalKMeans:
    """
    球面K-means聚类，用于单位球面上的标准化数据
    """
    def __init__(self, n_clusters=5, max_iter=300, tol=1e-4, random_state=None):
        self.n_clusters = n_clusters
        self.max_iter = max_iter
        self.tol = tol
        self.random_state = random_state
        
    def fit(self, X):
        """
        拟合球面k-means到数据
        """
        if self.random_state is not None:
            np.random.seed(self.random_state)
        
        # 标准化数据到单位球面
        X_normalized = normalize(X, norm='l2')
        n_samples, n_features = X_normalized.shape
        
        # 在单位球面上随机初始化质心
        centroids = np.random.randn(self.n_clusters, n_features)
        centroids = normalize(centroids, norm='l2')
        
        for iteration in range(self.max_iter):
            # 将点分配给最近的质心（使用余弦相似度）
            similarities = np.dot(X_normalized, centroids.T)
            labels = np.argmax(similarities, axis=1)
            
            # 更新质心
            new_centroids = np.zeros_like(centroids)
            for k in range(self.n_clusters):
                cluster_points = X_normalized[labels == k]
                if len(cluster_points) > 0:
                    # 聚类中点的均值，然后标准化
                    centroid = np.mean(cluster_points, axis=0)
                    new_centroids[k] = normalize(centroid.reshape(1, -1), norm='l2')[0]
                else:
                    # 如果没有点分配，保持旧质心
                    new_centroids[k] = centroids[k]
            
            # 检查收敛性
            centroid_shift = np.mean([np.linalg.norm(new_centroids[k] - centroids[k]) 
                                    for k in range(self.n_clusters)])
            
            centroids = new_centroids
            
            if centroid_shift < self.tol:
                break
        
        # 计算惯性（到质心的余弦距离之和）
        inertia = 0
        for i in range(n_samples):
            centroid_idx = labels[i]
            cosine_dist = 1 - np.dot(X_normalized[i], centroids[centroid_idx])
            inertia += cosine_dist
        
        self.cluster_centers_ = centroids
        self.labels_ = labels
        self.inertia_ = inertia
        
        return self
    
    def fit_predict(self, X):
        """
        拟合并预测聚类标签
        """
        self.fit(X)
        return self.labels_

def cluster_contingency(ref_labels_sub, boot_labels):
    """返回两个标签之间的列联表（相同长度的数组）"""
    # 创建列联矩阵
    ref_unique = np.unique(ref_labels_sub)
    boot_unique = np.unique(boot_labels)
    cont = np.zeros((len(ref_unique), len(boot_unique)), dtype=int)
    ref_to_idx = {v:i for i,v in enumerate(ref_unique)}
    boot_to_idx = {v:i for i,v in enumerate(boot_unique)}
    for r,b in zip(ref_labels_sub, boot_labels):
        cont[ref_to_idx[r], boot_to_idx[b]] += 1
    return cont, ref_unique, boot_unique

def per_cluster_jaccard(ref_labels_sub, boot_labels, n_clusters):
    """计算每个参考聚类与匹配的自助法聚类的Jaccard系数"""
    cont, ref_u, boot_u = cluster_contingency(ref_labels_sub, boot_labels)
    # 使用匈牙利算法在负重叠上匹配（最大化重叠）
    row_ind, col_ind = linear_sum_assignment(-cont)
    jaccards = np.zeros(n_clusters)
    for r_idx, b_idx in zip(row_ind, col_ind):
        # J(A,B) = |A∩B| / |A∪B| = intersection / (sizeA + sizeB - intersection)
        inter = cont[r_idx, b_idx]
        sizeA = cont[r_idx, :].sum()
        sizeB = cont[:, b_idx].sum()
        denom = sizeA + sizeB - inter
        j = inter / denom if denom > 0 else 0.0
        # 如果需要，映射回原始聚类id；这里我们按参考聚类顺序存储
        if r_idx < len(ref_u) and ref_u[r_idx] < n_clusters:
            jaccards[ref_u[r_idx]] = j
    return jaccards  # 长度为n_clusters（如果未匹配，某些索引可能保持为零）

def bootstrap_stability_analysis_optimized(X, n_clusters, n_bootstrap=100, sample_ratio=0.8, random_state=42):
    """
    使用多个指标对固定k执行优化的自助法稳定性分析
    """
    print(f"执行优化的自助法稳定性分析 (n_bootstrap={n_bootstrap})...")
    
    np.random.seed(random_state)
    X_normalized = normalize(X, norm='l2')
    n_samples = len(X)
    sample_size = int(n_samples * sample_ratio)
    
    # 在完整数据集上的参考聚类
    ref_kmeans = SphericalKMeans(n_clusters=n_clusters, random_state=random_state)
    ref_labels = ref_kmeans.fit_predict(X_normalized)
    
    # 初始化指标存储
    ari_list = []
    nmi_list = []
    cluster_jaccards = []  # 将是长度为n_clusters的数组列表
    consensus_counts = np.zeros((n_samples, n_samples), dtype=int)
    cooccurrence_counts = np.zeros((n_samples, n_samples), dtype=int)
    
    # 自助法迭代
    for i in tqdm(range(n_bootstrap), desc="自助法迭代"):
        # 有放回抽样
        bootstrap_indices = np.random.choice(n_samples, size=sample_size, replace=True)
        X_boot = X_normalized[bootstrap_indices]
        
        # 对自助法样本聚类
        boot_km = SphericalKMeans(n_clusters=n_clusters, random_state=random_state + i)
        boot_labels = boot_km.fit_predict(X_boot)
        
        # 比较自助法中存在的索引上的标签（映射到原始索引）
        ref_labels_on_boot = ref_labels[bootstrap_indices]
        
        # 在自助法样本部分上的ARI / NMI
        ari = adjusted_rand_score(ref_labels_on_boot, boot_labels)
        nmi = normalized_mutual_info_score(ref_labels_on_boot, boot_labels)
        ari_list.append(ari)
        nmi_list.append(nmi)
        
        # 聚类级别的Jaccard
        jacs = per_cluster_jaccard(ref_labels_on_boot, boot_labels, n_clusters)
        cluster_jaccards.append(jacs)
        
        # 更新共识/共关联：对于bootstrap_indices中的每对(u,v)，
        # 增加cooccurrence_counts[u,v]，如果标签相等则增加consensus_counts[u,v]
        for a_idx, a in enumerate(bootstrap_indices):
            for b_idx, b in enumerate(bootstrap_indices[a_idx+1:], start=a_idx+1):
                cooccurrence_counts[a, b] += 1
                cooccurrence_counts[b, a] += 1
                if boot_labels[a_idx] == boot_labels[b_idx]:
                    consensus_counts[a, b] += 1
                    consensus_counts[b, a] += 1
    
    # 循环后，计算共识频率矩阵（避免除以0）
    consensus_freq = np.zeros_like(consensus_counts, dtype=float)
    mask = cooccurrence_counts > 0
    consensus_freq[mask] = consensus_counts[mask] / cooccurrence_counts[mask]
    
    # 汇总结果
    cluster_jaccard_array = np.vstack(cluster_jaccards) if cluster_jaccards else np.zeros((n_bootstrap, n_clusters))
    
    results = {
        'ari_mean': np.mean(ari_list),
        'ari_std': np.std(ari_list),
        'ari_scores': ari_list,
        'nmi_mean': np.mean(nmi_list),
        'nmi_std': np.std(nmi_list),
        'nmi_scores': nmi_list,
        'cluster_jaccard_mean': np.nanmean(cluster_jaccard_array, axis=0),  # 每个聚类
        'cluster_jaccard_std': np.nanstd(cluster_jaccard_array, axis=0),
        'cluster_jaccard_scores': cluster_jaccard_array,
        'consensus_matrix': consensus_freq,
        'overall_stability': np.mean(ari_list),  # 使用ARI作为总体稳定性度量
        'stability_std': np.std(ari_list),
        'n_bootstrap': n_bootstrap,
        'sample_ratio': sample_ratio
    }
    
    print(f"稳定性分析完成:")
    print(f"  ARI: {results['ari_mean']:.4f} ± {results['ari_std']:.4f}")
    print(f"  NMI: {results['nmi_mean']:.4f} ± {results['nmi_std']:.4f}")
    print(f"  每聚类Jaccard: {results['cluster_jaccard_mean']}")
    
    return results

def load_synergy_data_with_groups():
    """
    加载带有组别信息的处理后协同数据
    """
    print("加载带有组别信息的协同数据...")
    
    if not os.path.exists(INPUT_DATA_PATH):
        print(f"数据文件未找到: {INPUT_DATA_PATH}")
        return None
    
    df = pd.read_csv(INPUT_DATA_PATH)
    print(f"加载数据形状: {df.shape}")
    print(f"列名: {list(df.columns)}")
    
    # 检查必需的列
    emg_columns = [f'EMG{i}' for i in range(1, 9)]
    required_columns = ['Time_Phase', 'Group', 'Group_Label']
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"缺少必需的列: {missing_columns}")
        return None
    
    # 检查EMG列
    available_emg_cols = [col for col in emg_columns if col in df.columns]
    if not available_emg_cols:
        print("错误: 未找到EMG列!")
        return None
    
    print(f"可用EMG列: {available_emg_cols}")
    print(f"可用时间阶段: {sorted(df['Time_Phase'].unique())}")
    print(f"可用组别: {sorted(df['Group'].unique())}")
    print(f"组别标签分布: {df['Group_Label'].value_counts().sort_index().to_dict()}")
    
    # 显示每个时间阶段的样本数
    print(f"\n按时间阶段的样本分布:")
    time_counts = df.groupby('Time_Phase').size().reset_index(name='count')
    for _, row in time_counts.iterrows():
        print(f"  时间阶段 {row['Time_Phase']}: {row['count']} 个样本")
    
    # 显示每个组别的样本数
    print(f"\n按组别的样本分布:")
    group_counts = df.groupby(['Group', 'Group_Label']).size().reset_index(name='count')
    for _, row in group_counts.iterrows():
        print(f"  {row['Group']} (标签{row['Group_Label']}): {row['count']} 个样本")
    
    return df

if __name__ == "__main__":
    print("=== 三类人群固定K球面聚类优化分析 ===")
    print(f"固定K: {FIXED_K}")
    print(f"输入数据: {INPUT_DATA_PATH}")
    print(f"输出目录: {OUTPUT_BASE_DIR}")
    
    # 创建输出目录
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)
    
    # 加载数据
    df = load_synergy_data_with_groups()
    if df is None:
        print("加载数据失败。退出。")
        exit()
    
    print("\n数据加载成功！")

def extract_phase_data_with_groups(df, time_phase):
    """
    提取特定时间阶段的数据，包含组别标签
    """
    print(f"提取时间阶段 '{time_phase}' 的数据")

    # 过滤数据
    phase_data = df[df['Time_Phase'] == time_phase].copy()

    if len(phase_data) == 0:
        print(f"未找到时间阶段 '{time_phase}' 的数据")
        return None, None

    # 提取EMG数据
    emg_columns = [f'EMG{i}' for i in range(1, 9)]
    available_emg_cols = [col for col in emg_columns if col in phase_data.columns]
    X = phase_data[available_emg_cols].values

    # 创建带有组别信息的文件信息数据框
    info_columns = ['Time_Phase', 'Group', 'Group_Label']
    if 'File_ID' in phase_data.columns:
        info_columns.append('File_ID')
    if 'Synergy_Index' in phase_data.columns:
        info_columns.append('Synergy_Index')
    if 'Person_Name' in phase_data.columns:
        info_columns.append('Person_Name')

    file_info_df = phase_data[info_columns].copy()

    # 添加人群标签 (0: healthy, 1: PDearly, 2: PDmid)
    file_info_df['population_label'] = file_info_df['Group_Label']

    # 添加人群名称
    population_names = {0: '健康人群', 1: '早期PD', 2: '中期PD'}
    file_info_df['population_name'] = file_info_df['population_label'].map(population_names)

    print(f"时间阶段 {time_phase}: {len(X)} 个样本")
    group_counts = file_info_df['population_label'].value_counts().sort_index()
    for pop_label, count in group_counts.items():
        group_name = population_names.get(pop_label, f'未知组别{pop_label}')
        print(f"  {group_name}: {count} 个样本")

    return X, file_info_df

def perform_fixed_clustering(X, n_clusters, random_state=42):
    """
    使用固定聚类数执行球面聚类
    """
    print(f"使用 k={n_clusters} 执行球面聚类...")

    # 标准化数据到单位球面
    X_normalized = normalize(X, norm='l2')

    # 执行球面k-means
    spherical_kmeans = SphericalKMeans(n_clusters=n_clusters, random_state=random_state)
    labels = spherical_kmeans.fit(X_normalized).labels_

    # 计算指标
    if len(np.unique(labels)) > 1:
        sil_score = silhouette_score(X_normalized, labels, metric='cosine')
        db_index = davies_bouldin_score(X_normalized, labels)
    else:
        sil_score = 0
        db_index = np.inf

    results = {
        'model': spherical_kmeans,
        'labels': labels,
        'n_clusters': n_clusters,
        'silhouette_score': sil_score,
        'db_index': db_index,
        'inertia': spherical_kmeans.inertia_
    }

    print(f"聚类结果: 轮廓系数={sil_score:.4f}, DB指数={db_index:.4f}")

    return results

def save_stability_analysis_results(stability_results, output_dir, phase_name):
    """
    保存详细的稳定性分析结果
    """
    print("保存稳定性分析结果...")

    # 保存共识矩阵
    consensus_file = os.path.join(output_dir, "consensus_matrix.csv")
    pd.DataFrame(stability_results['consensus_matrix']).to_csv(consensus_file, index=False)

    # 保存详细指标
    metrics_data = {
        'bootstrap_iteration': range(stability_results['n_bootstrap']),
        'ari_score': stability_results['ari_scores'],
        'nmi_score': stability_results['nmi_scores']
    }

    # 添加每聚类Jaccard分数
    for i in range(len(stability_results['cluster_jaccard_mean'])):
        metrics_data[f'cluster_{i}_jaccard'] = stability_results['cluster_jaccard_scores'][:, i]

    metrics_df = pd.DataFrame(metrics_data)
    metrics_file = os.path.join(output_dir, "bootstrap_metrics_detailed.csv")
    metrics_df.to_csv(metrics_file, index=False)

    # 保存汇总统计
    summary_data = {
        'metric': ['ARI', 'NMI'] + [f'Cluster_{i}_Jaccard' for i in range(len(stability_results['cluster_jaccard_mean']))],
        'mean': [stability_results['ari_mean'], stability_results['nmi_mean']] + list(stability_results['cluster_jaccard_mean']),
        'std': [stability_results['ari_std'], stability_results['nmi_std']] + list(stability_results['cluster_jaccard_std'])
    }

    summary_df = pd.DataFrame(summary_data)
    summary_file = os.path.join(output_dir, "stability_summary.csv")
    summary_df.to_csv(summary_file, index=False)

    # 创建稳定性可视化
    plot_stability_results(stability_results, output_dir, phase_name)

    print(f"稳定性分析结果保存到: {output_dir}")

def plot_stability_results(stability_results, output_dir, phase_name):
    """
    为稳定性分析结果创建可视化
    """
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # ARI分布
    axes[0, 0].hist(stability_results['ari_scores'], bins=20, alpha=0.7, color='blue')
    axes[0, 0].axvline(stability_results['ari_mean'], color='red', linestyle='--',
                      label=f'均值: {stability_results["ari_mean"]:.3f}')
    axes[0, 0].set_xlabel('ARI分数')
    axes[0, 0].set_ylabel('频率')
    axes[0, 0].set_title('ARI分数分布')
    axes[0, 0].legend()

    # NMI分布
    axes[0, 1].hist(stability_results['nmi_scores'], bins=20, alpha=0.7, color='green')
    axes[0, 1].axvline(stability_results['nmi_mean'], color='red', linestyle='--',
                      label=f'均值: {stability_results["nmi_mean"]:.3f}')
    axes[0, 1].set_xlabel('NMI分数')
    axes[0, 1].set_ylabel('频率')
    axes[0, 1].set_title('NMI分数分布')
    axes[0, 1].legend()

    # 每聚类Jaccard分数
    cluster_jaccard_scores = stability_results['cluster_jaccard_scores']
    n_clusters = cluster_jaccard_scores.shape[1]

    for i in range(n_clusters):
        axes[1, 0].hist(cluster_jaccard_scores[:, i], bins=15, alpha=0.5,
                       label=f'聚类 {i}')
    axes[1, 0].set_xlabel('Jaccard分数')
    axes[1, 0].set_ylabel('频率')
    axes[1, 0].set_title('每聚类Jaccard分数分布')
    axes[1, 0].legend()

    # 共识矩阵热图
    consensus_matrix = stability_results['consensus_matrix']
    im = axes[1, 1].imshow(consensus_matrix, cmap='viridis', aspect='auto')
    axes[1, 1].set_xlabel('样本索引')
    axes[1, 1].set_ylabel('样本索引')
    axes[1, 1].set_title('共识矩阵')
    plt.colorbar(im, ax=axes[1, 1])

    plt.suptitle(f'稳定性分析结果 - {phase_name}', fontsize=16)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "stability_analysis_plots.png"),
                dpi=300, bbox_inches='tight')
    plt.close()

def analyze_cluster_population_distribution(cluster_labels, population_labels, output_dir, phase_name):
    """
    分析和可视化聚类-人群分布
    """
    print("分析聚类-人群分布...")

    unique_clusters = np.unique(cluster_labels)
    unique_populations = np.unique(population_labels)

    # 创建列联表
    contingency_matrix = np.zeros((len(unique_clusters), len(unique_populations)))

    for i, cluster in enumerate(unique_clusters):
        for j, pop in enumerate(unique_populations):
            count = np.sum((cluster_labels == cluster) & (population_labels == pop))
            contingency_matrix[i, j] = count

    # 创建数据框
    pop_names = ['健康人群', '早期PD', '中期PD'][:len(unique_populations)]
    contingency_df = pd.DataFrame(
        contingency_matrix,
        index=[f'聚类_{i}' for i in unique_clusters],
        columns=pop_names
    )

    # 计算百分比
    cluster_totals = contingency_matrix.sum(axis=1, keepdims=True)
    cluster_percentages = contingency_matrix / cluster_totals * 100
    cluster_perc_df = pd.DataFrame(
        cluster_percentages,
        index=[f'聚类_{i}' for i in unique_clusters],
        columns=pop_names
    )

    pop_totals = contingency_matrix.sum(axis=0, keepdims=True)
    pop_percentages = contingency_matrix / pop_totals * 100
    pop_perc_df = pd.DataFrame(
        pop_percentages,
        index=[f'聚类_{i}' for i in unique_clusters],
        columns=pop_names
    )

    # 可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 列联表热图
    sns.heatmap(contingency_df, annot=True, fmt='g', cmap='Blues', ax=axes[0, 0])
    axes[0, 0].set_title('聚类-人群列联表')
    axes[0, 0].set_xlabel('人群')
    axes[0, 0].set_ylabel('聚类')

    # 聚类百分比热图
    sns.heatmap(cluster_perc_df, annot=True, fmt='.1f', cmap='Oranges', ax=axes[0, 1])
    axes[0, 1].set_title('聚类组成 (%)')
    axes[0, 1].set_xlabel('人群')
    axes[0, 1].set_ylabel('聚类')

    # 堆叠条形图
    contingency_df.plot(kind='bar', stacked=True, ax=axes[1, 0])
    axes[1, 0].set_title('聚类组成 (堆叠)')
    axes[1, 0].set_xlabel('聚类')
    axes[1, 0].set_ylabel('样本数量')
    axes[1, 0].legend(title='人群')

    # 人群在聚类中的分布
    contingency_df.T.plot(kind='bar', ax=axes[1, 1])
    axes[1, 1].set_title('人群在聚类中的分布')
    axes[1, 1].set_xlabel('人群')
    axes[1, 1].set_ylabel('样本数量')
    axes[1, 1].legend(title='聚类')

    plt.suptitle(f'聚类-人群分析 - {phase_name}', fontsize=16)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "cluster_population_distribution.png"),
                dpi=300, bbox_inches='tight')
    plt.close()

    # 保存表格
    contingency_df.to_csv(os.path.join(output_dir, "cluster_population_contingency.csv"))
    cluster_perc_df.to_csv(os.path.join(output_dir, "cluster_composition_percentages.csv"))
    pop_perc_df.to_csv(os.path.join(output_dir, "population_distribution_percentages.csv"))

    print(f"聚类-人群分析保存到: {output_dir}")

    return contingency_df, cluster_perc_df, pop_perc_df

def plot_clustering_results(X, cluster_labels, population_labels, clustering_results, output_dir, phase_name):
    """
    绘制带有人群信息的聚类结果
    """
    print("绘制聚类结果...")

    X_normalized = normalize(X, norm='l2')

    # PCA用于可视化
    from sklearn.decomposition import PCA
    pca = PCA(n_components=2, random_state=42)
    X_pca = pca.fit_transform(X_normalized)

    # 创建图表
    fig, axes = plt.subplots(1, 2, figsize=(15, 6))

    # 按聚类绘制
    scatter1 = axes[0].scatter(X_pca[:, 0], X_pca[:, 1], c=cluster_labels, cmap='tab10', alpha=0.7)
    axes[0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} 方差)')
    axes[0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} 方差)')
    axes[0].set_title('聚类结果 (按聚类)')
    plt.colorbar(scatter1, ax=axes[0])

    # 按人群绘制
    colors = ['skyblue', 'lightgreen', 'lightcoral']
    pop_names = ['健康人群', '早期PD', '中期PD']

    for pop_label in np.unique(population_labels):
        mask = population_labels == pop_label
        if np.any(mask):
            axes[1].scatter(X_pca[mask, 0], X_pca[mask, 1],
                           c=colors[int(pop_label)], label=pop_names[int(pop_label)],
                           alpha=0.7, s=50)

    axes[1].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} 方差)')
    axes[1].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} 方差)')
    axes[1].set_title('聚类结果 (按人群)')
    axes[1].legend()

    plt.suptitle(f'聚类可视化 - {phase_name}', fontsize=16)
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "clustering_visualization.png"),
                dpi=300, bbox_inches='tight')
    plt.close()

def save_final_results(X, file_info_df, clustering_results, stability_results, output_dir, phase_name):
    """
    保存带有所有信息的最终聚类结果
    """
    print("保存最终结果...")

    # 创建结果数据框
    results_df = file_info_df.copy()

    # 添加EMG数据
    emg_columns = [f'EMG{i}' for i in range(1, 9)]
    for i, col in enumerate(emg_columns):
        if i < X.shape[1]:
            results_df[col] = X[:, i]

    # 添加标准化EMG数据
    X_normalized = normalize(X, norm='l2')
    for i, col in enumerate(emg_columns):
        if i < X_normalized.shape[1]:
            results_df[f'{col}_norm'] = X_normalized[:, i]

    # 添加聚类结果
    results_df['Cluster_Label'] = clustering_results['labels']

    # 添加稳定性分数（从共识矩阵的每样本）
    consensus_matrix = stability_results['consensus_matrix']
    stability_scores = np.mean(consensus_matrix, axis=1)  # 与所有其他样本的平均共识
    results_df['Stability_Score'] = stability_scores

    # 保存主要结果
    results_file = os.path.join(output_dir, "final_clustering_results.csv")
    results_df.to_csv(results_file, index=False)

    # 保存聚类摘要
    summary_data = {
        'phase_name': [phase_name],
        'fixed_k': [FIXED_K],
        'silhouette_score': [clustering_results['silhouette_score']],
        'db_index': [clustering_results['db_index']],
        'inertia': [clustering_results['inertia']],
        'n_samples': [len(X)],
        'n_healthy': [np.sum(file_info_df['population_label'] == 0)],
        'n_PDearly': [np.sum(file_info_df['population_label'] == 1)],
        'n_PDmid': [np.sum(file_info_df['population_label'] == 2)],
        'overall_stability': [stability_results['overall_stability']],
        'ari_mean': [stability_results['ari_mean']],
        'nmi_mean': [stability_results['nmi_mean']],
        'n_bootstrap': [stability_results['n_bootstrap']],
        'sample_ratio': [stability_results['sample_ratio']]
    }

    summary_df = pd.DataFrame(summary_data)
    summary_file = os.path.join(output_dir, "clustering_summary.csv")
    summary_df.to_csv(summary_file, index=False)

    print(f"最终结果保存到: {output_dir}")

    return results_df, summary_df

def process_single_phase(df, time_phase):
    """
    使用优化稳定性分析处理单个时间阶段
    """
    phase_name = f"time{time_phase}"
    print(f"\n{'='*60}")
    print(f"处理阶段: {phase_name}")
    print(f"{'='*60}")

    # 提取阶段数据
    X, file_info_df = extract_phase_data_with_groups(df, time_phase)

    if X is None:
        print(f"跳过阶段 {phase_name} - 无数据")
        return None

    # 创建输出目录
    output_dir = os.path.join(OUTPUT_BASE_DIR, phase_name)
    os.makedirs(output_dir, exist_ok=True)

    # 执行聚类
    clustering_results = perform_fixed_clustering(X, FIXED_K)

    # 执行优化稳定性分析
    stability_results = bootstrap_stability_analysis_optimized(
        X, FIXED_K, n_bootstrap=100, sample_ratio=0.8
    )

    # 保存稳定性分析结果
    save_stability_analysis_results(stability_results, output_dir, phase_name)

    # 分析聚类-人群分布
    analyze_cluster_population_distribution(
        clustering_results['labels'],
        file_info_df['population_label'].values,
        output_dir,
        phase_name
    )

    # 绘制结果
    plot_clustering_results(
        X,
        clustering_results['labels'],
        file_info_df['population_label'].values,
        clustering_results,
        output_dir,
        phase_name
    )

    # 保存最终结果
    results_df, summary_df = save_final_results(
        X, file_info_df, clustering_results, stability_results, output_dir, phase_name
    )

    print(f"阶段 {phase_name} 处理完成!")

    return {
        'phase_name': phase_name,
        'results_df': results_df,
        'summary_df': summary_df,
        'clustering_results': clustering_results,
        'stability_results': stability_results
    }

def main():
    """
    使用优化稳定性分析处理所有阶段的主函数
    """
    print("=== 三类人群固定K球面聚类优化稳定性分析 ===")
    print(f"固定K: {FIXED_K}")
    print(f"输出目录: {OUTPUT_BASE_DIR}")

    # 加载数据
    df = load_synergy_data_with_groups()
    if df is None:
        return

    # 创建输出目录
    os.makedirs(OUTPUT_BASE_DIR, exist_ok=True)

    # 获取所有唯一时间阶段
    time_phases = sorted(df['Time_Phase'].unique())

    print(f"\n找到 {len(time_phases)} 个时间阶段:")
    for time_phase in time_phases:
        print(f"  时间阶段 {time_phase}")

    # 处理每个时间阶段
    all_results = []
    all_summaries = []

    for time_phase in time_phases:
        result = process_single_phase(df, time_phase)

        if result is not None:
            all_results.append(result)
            all_summaries.append(result['summary_df'])

    # 保存合并摘要
    if all_summaries:
        combined_summary = pd.concat(all_summaries, ignore_index=True)
        combined_summary_file = os.path.join(OUTPUT_BASE_DIR, "all_phases_summary.csv")
        combined_summary.to_csv(combined_summary_file, index=False)

        # 创建详细报告
        report_file = os.path.join(OUTPUT_BASE_DIR, "analysis_report.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=== 三类人群固定K球面聚类分析报告 ===\n\n")
            f.write(f"固定聚类数: {FIXED_K}\n")
            f.write(f"处理的总阶段数: {len(time_phases)}\n")
            f.write(f"成功处理: {len(all_results)}\n\n")

            f.write("阶段结果:\n")
            f.write("-" * 120 + "\n")
            f.write(f"{'阶段':<10} {'样本':<6} {'健康/早期/中期':<15} {'轮廓系数':<10} {'DB指数':<8} {'ARI':<8} {'NMI':<8} {'稳定性':<8}\n")
            f.write("-" * 120 + "\n")

            for _, row in combined_summary.iterrows():
                group_str = f"{int(row['n_healthy'])}/{int(row['n_PDearly'])}/{int(row['n_PDmid'])}"
                f.write(f"{row['phase_name']:<10} {int(row['n_samples']):<6} "
                       f"{group_str:<15} {row['silhouette_score']:<10.4f} {row['db_index']:<8.4f} "
                       f"{row['ari_mean']:<8.4f} {row['nmi_mean']:<8.4f} {row['overall_stability']:<8.4f}\n")

            f.write("\n" + "=" * 120 + "\n")
            f.write("分析完成成功!\n")
            f.write(f"所有结果保存到: {OUTPUT_BASE_DIR}\n")

        print(f"\n{'='*60}")
        print(f"所有阶段处理完成")
        print(f"{'='*60}")
        print(f"成功处理 {len(all_results)} 个阶段")
        print(f"合并摘要保存到: {combined_summary_file}")
        print(f"详细报告保存到: {report_file}")
        print(f"各阶段结果保存在: {OUTPUT_BASE_DIR}")

        # 显示最佳表现阶段
        print(f"\n表现最佳的阶段 (按ARI分数):")
        sorted_summaries = combined_summary.sort_values('ari_mean', ascending=False)
        for i, (_, row) in enumerate(sorted_summaries.head(5).iterrows(), 1):
            print(f"  {i}. {row['phase_name']}: ARI={row['ari_mean']:.4f}, 轮廓系数={row['silhouette_score']:.4f}, 样本={int(row['n_samples'])}")

if __name__ == "__main__":
    main()
