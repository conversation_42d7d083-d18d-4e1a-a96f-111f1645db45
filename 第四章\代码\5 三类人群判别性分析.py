import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import f_oneway
import glob
from sklearn.preprocessing import StandardScaler

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_fixed_k_results(base_dir):
    """
    加载固定K值聚类结果文件
    """
    print(f"在目录中搜索CSV文件: {base_dir}")
    
    # 查找所有CSV文件
    csv_files = glob.glob(os.path.join(base_dir, "*.csv"))
    
    if not csv_files:
        print("未找到CSV文件!")
        return {}
    
    print(f"找到 {len(csv_files)} 个CSV文件:")
    
    data_dict = {}
    
    for file_path in csv_files:
        file_name = os.path.basename(file_path)
        print(f"  加载: {file_name}")
        
        try:
            df = pd.read_csv(file_path)
            
            # 检查必要的列是否存在
            required_cols = ['population_label', 'Cluster_Label'] + [f'EMG{i}' for i in range(1, 9)]
            missing_cols = [col for col in required_cols if col not in df.columns]
            
            if missing_cols:
                print(f"    警告: 缺少列 {missing_cols}")
                continue
            
            print(f"    形状: {df.shape}")
            print(f"    人群: {sorted(df['population_label'].unique())}")
            print(f"    聚类: {sorted(df['Cluster_Label'].unique())}")
            print(f"    时间阶段: {df['Time_Phase'].iloc[0] if 'Time_Phase' in df.columns else '未知'}")
            
            data_dict[file_name] = df
            
        except Exception as e:
            print(f"    加载 {file_name} 时出错: {e}")
    
    return data_dict

def parse_fixed_k_filename(filename):
    """
    从固定K值结果文件名解析Phase信息
    例如: L_0.csv -> Side=L, Phase=0
    """
    base_name = filename.replace('.csv', '')
    parts = base_name.split('_')
    
    if len(parts) >= 2:
        side = parts[0]  # L or R
        phase = parts[1]  # 0, 1, 2, etc.
        return side, phase
    else:
        return '未知', '未知'

def calculate_discriminative_scores_fixed_k(df, phase_info):
    """
    计算每个(Phase, Pattern, Channel)的discriminative score
    针对固定K值聚类结果进行分析 - 适用于三类人群
    """
    side, phase = phase_info
    emg_columns = [f'EMG{i}' for i in range(1, 9)]
    
    # 创建人群标签映射 - 适用于三类人群
    unique_populations = sorted(df['population_label'].unique())
    population_names = {0: '健康人群', 1: '早期PD', 2: '中期PD'}
    
    # 处理实际存在的人群
    actual_pop_names = {}
    for pop in unique_populations:
        if pop in population_names:
            actual_pop_names[pop] = population_names[pop]
        else:
            actual_pop_names[pop] = f'人群_{pop}'
    
    print(f"人群映射: {actual_pop_names}")
    
    results = []
    
    for cluster in sorted(df['Cluster_Label'].unique()):
        cluster_data = df[df['Cluster_Label'] == cluster]
        
        for emg_col in emg_columns:
            # 准备各组数据
            groups_data = []
            group_labels = []
            
            for pop_label in sorted(cluster_data['population_label'].unique()):
                pop_data = cluster_data[cluster_data['population_label'] == pop_label]
                values = pop_data[emg_col].values
                
                if len(values) > 0:
                    groups_data.append(values)
                    group_labels.append(actual_pop_names.get(pop_label, f'人群_{pop_label}'))
            
            if len(groups_data) >= 2:  # 至少需要两组进行比较
                try:
                    # 计算ANOVA F值
                    f_statistic, p_value = f_oneway(*groups_data)
                    
                    # 计算η² (eta squared)
                    all_values = np.concatenate(groups_data)
                    grand_mean = np.mean(all_values)
                    
                    # 组间平方和 (SS_between)
                    ss_between = 0
                    for i, group in enumerate(groups_data):
                        group_mean = np.mean(group)
                        ss_between += len(group) * (group_mean - grand_mean) ** 2
                    
                    # 总平方和 (SS_total)
                    ss_total = np.sum((all_values - grand_mean) ** 2)
                    
                    # η²
                    eta_squared = ss_between / ss_total if ss_total > 0 else 0
                    
                    # 计算Cohen's d (对于两组比较)
                    if len(groups_data) == 2:
                        group1, group2 = groups_data
                        pooled_std = np.sqrt(((len(group1) - 1) * np.var(group1, ddof=1) + 
                                            (len(group2) - 1) * np.var(group2, ddof=1)) / 
                                           (len(group1) + len(group2) - 2))
                        cohen_d = abs(np.mean(group1) - np.mean(group2)) / pooled_std if pooled_std > 0 else 0
                    else:
                        # 对于三组比较，计算最大组间差异的Cohen's d
                        group_means = [np.mean(group) for group in groups_data]
                        max_diff = max(group_means) - min(group_means)
                        pooled_std = np.sqrt(np.mean([np.var(group, ddof=1) for group in groups_data]))
                        cohen_d = max_diff / pooled_std if pooled_std > 0 else 0
                    
                    # 计算各组统计量
                    group_means = [np.mean(group) for group in groups_data]
                    group_stds = [np.std(group, ddof=1) for group in groups_data]
                    
                    results.append({
                        'Side': side,
                        'Phase': phase,
                        'Pattern': f'聚类_{cluster}',
                        'Channel': emg_col,
                        'F_statistic': f_statistic,
                        'P_value': p_value,
                        'Eta_squared': eta_squared,
                        'Cohen_d': cohen_d,
                        'N_groups': len(groups_data),
                        'Group_labels': group_labels,
                        'Group_means': group_means,
                        'Group_stds': group_stds,
                        'Total_samples': len(all_values)
                    })
                    
                except Exception as e:
                    print(f"计算 {side}_{phase}, 聚类_{cluster}, {emg_col} 的分数时出错: {e}")
    
    return pd.DataFrame(results)

def create_discriminative_heatmaps_fixed_k(all_results, output_dir):
    """
    创建discriminative score热图 - 针对固定K值结果
    """
    if all_results.empty:
        print("没有结果可绘制")
        return
    
    # 创建不同指标的热图
    metrics = ['F_statistic', 'Eta_squared', 'Cohen_d']
    metric_names = {'F_statistic': 'F统计量', 'Eta_squared': 'η²效应量', 'Cohen_d': "Cohen's d"}
    
    for metric in metrics:
        print(f"创建 {metric_names[metric]} 热图...")
        
        # 创建透视表 - 转置版本以便更好显示
        pivot_data = all_results.pivot_table(
            values=metric,
            index=['Side', 'Phase', 'Pattern'],
            columns='Channel',
            aggfunc='mean'
        ).T
        
        if pivot_data.empty:
            print(f"没有 {metric} 热图的数据")
            continue
        
        # 调整图形大小
        plt.figure(figsize=(max(16, len(pivot_data.columns) * 1.5), 8))
        
        # 选择合适的颜色映射
        if metric == 'P_value':
            # P值用反向颜色（越小越红）
            cmap = 'RdYlBu_r'
            vmax = 0.05
        else:
            # F值、η²、Cohen's d用正向颜色（越大越红）
            cmap = 'YlOrRd'
            vmax = None
        
        sns.heatmap(
            pivot_data,
            annot=False,
            fmt='.2f',
            cmap=cmap,
            center=None,
            vmax=vmax,
            cbar_kws={'label': metric_names[metric]},
            xticklabels=True,
            yticklabels=True
        )
        
        plt.title(f'判别性分数热图 - {metric_names[metric]} (固定K)', 
                 fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('阶段_模式', fontsize=14, fontweight='bold')
        plt.ylabel('EMG通道', fontsize=14, fontweight='bold')
        
        # 旋转x轴标签以便更好显示
        plt.xticks(rotation=45, ha='right')
        plt.yticks(rotation=0)
        
        plt.tight_layout()
        
        # 保存图片
        output_path = os.path.join(output_dir, f'判别性热图_{metric}_固定K.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"  已保存: 判别性热图_{metric}_固定K.png")

def create_top_discriminative_features_fixed_k(all_results, output_dir, top_n=20):
    """
    创建top discriminative features图表 - 针对固定K值结果
    """
    if all_results.empty:
        return

    print(f"创建前 {top_n} 个判别性特征图表...")

    # 按不同指标排序
    metrics = ['F_statistic', 'Eta_squared', 'Cohen_d']
    metric_names = {'F_statistic': 'F统计量', 'Eta_squared': 'η²效应量', 'Cohen_d': "Cohen's d"}

    for metric in metrics:
        print(f"创建 {metric_names[metric]} 的前{top_n}特征图表...")

        # 排序并取前N个
        top_features = all_results.nlargest(top_n, metric).copy()
        top_features['Feature_ID'] = (
            top_features['Side'] + '_' +
            top_features['Phase'] + '_' +
            top_features['Pattern'] + '_' +
            top_features['Channel']
        )

        # 创建条形图
        plt.figure(figsize=(12, max(8, top_n * 0.4)))

        bars = plt.barh(range(len(top_features)), top_features[metric])

        # 颜色编码：根据显著性
        colors = []
        for p_val in top_features['P_value']:
            if p_val < 0.001:
                colors.append('darkred')
            elif p_val < 0.01:
                colors.append('red')
            elif p_val < 0.05:
                colors.append('orange')
            else:
                colors.append('lightgray')

        for bar, color in zip(bars, colors):
            bar.set_color(color)

        plt.yticks(range(len(top_features)), top_features['Feature_ID'])
        plt.xlabel(metric_names[metric], fontsize=14, fontweight='bold')
        plt.title(f'前 {top_n} 个判别性特征 - {metric_names[metric]} (固定K)',
                 fontsize=16, fontweight='bold')

        # 添加图例
        from matplotlib.patches import Patch
        legend_elements = [
            Patch(facecolor='darkred', label='p < 0.001'),
            Patch(facecolor='red', label='p < 0.01'),
            Patch(facecolor='orange', label='p < 0.05'),
            Patch(facecolor='lightgray', label='p ≥ 0.05')
        ]
        plt.legend(handles=legend_elements, loc='lower right')

        plt.tight_layout()

        # 保存图片
        output_path = os.path.join(output_dir, f'前{top_n}判别性特征_{metric}_固定K.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"  已保存: 前{top_n}判别性特征_{metric}_固定K.png")

def create_phase_comparison_analysis(all_results, output_dir):
    """
    创建不同阶段的比较分析
    """
    if all_results.empty:
        return

    print("创建阶段比较分析...")

    # 按Phase分组统计
    phase_stats = all_results.groupby('Phase').agg({
        'F_statistic': ['mean', 'max', 'std', 'count'],
        'Eta_squared': ['mean', 'max', 'std'],
        'P_value': lambda x: (x < 0.05).sum()
    }).round(4)

    # 保存统计结果
    phase_stats.to_csv(os.path.join(output_dir, '阶段比较统计.csv'))

    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. 各阶段平均F统计量
    phase_f_means = all_results.groupby('Phase')['F_statistic'].mean()
    axes[0, 0].bar(phase_f_means.index, phase_f_means.values, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各阶段平均F统计量', fontweight='bold')
    axes[0, 0].set_xlabel('阶段')
    axes[0, 0].set_ylabel('F统计量')

    # 2. 各阶段平均η²
    phase_eta_means = all_results.groupby('Phase')['Eta_squared'].mean()
    axes[0, 1].bar(phase_eta_means.index, phase_eta_means.values, color='lightcoral', alpha=0.7)
    axes[0, 1].set_title('各阶段平均η²效应量', fontweight='bold')
    axes[0, 1].set_xlabel('阶段')
    axes[0, 1].set_ylabel('η²效应量')

    # 3. 各阶段显著特征数量
    phase_sig_counts = all_results.groupby('Phase').apply(lambda x: (x['P_value'] < 0.05).sum())
    axes[1, 0].bar(phase_sig_counts.index, phase_sig_counts.values, color='lightgreen', alpha=0.7)
    axes[1, 0].set_title('各阶段显著特征数量', fontweight='bold')
    axes[1, 0].set_xlabel('阶段')
    axes[1, 0].set_ylabel('数量')

    # 4. 各阶段F统计量分布箱线图
    phases = sorted(all_results['Phase'].unique())
    f_data = [all_results[all_results['Phase'] == phase]['F_statistic'].values for phase in phases]
    axes[1, 1].boxplot(f_data, labels=phases)
    axes[1, 1].set_title('各阶段F统计量分布', fontweight='bold')
    axes[1, 1].set_xlabel('阶段')
    axes[1, 1].set_ylabel('F统计量')

    plt.suptitle('阶段比较分析 (固定K)', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, '阶段比较分析.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"  已保存: 阶段比较分析.png")

def create_population_comparison_analysis(all_results, output_dir):
    """
    创建不同人群的比较分析
    """
    if all_results.empty:
        return

    print("创建人群比较分析...")

    # 分析每个特征的组间差异
    population_analysis = []

    for _, row in all_results.iterrows():
        feature_id = f"{row['Side']}_{row['Phase']}_{row['Pattern']}_{row['Channel']}"

        analysis_row = {
            'Feature_ID': feature_id,
            'Side': row['Side'],
            'Phase': row['Phase'],
            'Pattern': row['Pattern'],
            'Channel': row['Channel'],
            'F_statistic': row['F_statistic'],
            'P_value': row['P_value'],
            'Eta_squared': row['Eta_squared'],
            'Cohen_d': row['Cohen_d'],
            'N_groups': row['N_groups'],
            'Total_samples': row['Total_samples']
        }

        # 添加各组的均值和标准差
        if isinstance(row['Group_labels'], list) and isinstance(row['Group_means'], list):
            for i, (label, mean, std) in enumerate(zip(row['Group_labels'], row['Group_means'], row['Group_stds'])):
                analysis_row[f'{label}_mean'] = mean
                analysis_row[f'{label}_std'] = std

        population_analysis.append(analysis_row)

    population_df = pd.DataFrame(population_analysis)

    # 保存详细分析结果
    population_df.to_csv(os.path.join(output_dir, '人群比较详细分析.csv'), index=False)

    # 创建可视化
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. Effect size分布
    axes[0, 0].hist(all_results['Eta_squared'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].axvline(0.01, color='orange', linestyle='--', label='小效应 (0.01)')
    axes[0, 0].axvline(0.06, color='red', linestyle='--', label='中等效应 (0.06)')
    axes[0, 0].axvline(0.14, color='darkred', linestyle='--', label='大效应 (0.14)')
    axes[0, 0].set_title('效应量分布 (η²)', fontweight='bold')
    axes[0, 0].set_xlabel('η²效应量')
    axes[0, 0].set_ylabel('频次')
    axes[0, 0].legend()

    # 2. P值分布
    axes[0, 1].hist(all_results['P_value'], bins=30, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0, 1].axvline(0.05, color='red', linestyle='--', label='α = 0.05')
    axes[0, 1].axvline(0.01, color='darkred', linestyle='--', label='α = 0.01')
    axes[0, 1].set_title('P值分布', fontweight='bold')
    axes[0, 1].set_xlabel('P值')
    axes[0, 1].set_ylabel('频次')
    axes[0, 1].legend()

    # 3. 显著性水平统计
    sig_levels = ['p < 0.001', 'p < 0.01', 'p < 0.05', 'p ≥ 0.05']
    sig_counts = [
        (all_results['P_value'] < 0.001).sum(),
        ((all_results['P_value'] >= 0.001) & (all_results['P_value'] < 0.01)).sum(),
        ((all_results['P_value'] >= 0.01) & (all_results['P_value'] < 0.05)).sum(),
        (all_results['P_value'] >= 0.05).sum()
    ]

    colors = ['darkred', 'red', 'orange', 'lightgray']
    axes[1, 0].pie(sig_counts, labels=sig_levels, colors=colors, autopct='%1.1f%%', startangle=90)
    axes[1, 0].set_title('显著性水平分布', fontweight='bold')

    # 4. F统计量 vs η²散点图
    scatter = axes[1, 1].scatter(all_results['F_statistic'], all_results['Eta_squared'],
                                c=all_results['P_value'], cmap='RdYlBu_r', alpha=0.6)
    axes[1, 1].set_xlabel('F统计量')
    axes[1, 1].set_ylabel('η²效应量')
    axes[1, 1].set_title('F统计量 vs 效应量', fontweight='bold')
    plt.colorbar(scatter, ax=axes[1, 1], label='P值')

    plt.suptitle('人群比较分析 (固定K)', fontsize=16, fontweight='bold')
    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, '人群比较分析.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"  已保存: 人群比较分析.png")

def create_summary_statistics_fixed_k(all_results, output_dir):
    """
    创建汇总统计 - 针对固定K值结果
    """
    if all_results.empty:
        return

    print("创建汇总统计...")

    # 总体统计
    summary_stats = {
        'total_features': len(all_results),
        'total_phases': all_results['Phase'].nunique(),
        'total_patterns': all_results['Pattern'].nunique(),
        'total_channels': all_results['Channel'].nunique(),
        'mean_f_statistic': all_results['F_statistic'].mean(),
        'std_f_statistic': all_results['F_statistic'].std(),
        'mean_eta_squared': all_results['Eta_squared'].mean(),
        'std_eta_squared': all_results['Eta_squared'].std(),
        'mean_cohen_d': all_results['Cohen_d'].mean(),
        'std_cohen_d': all_results['Cohen_d'].std(),
        'significant_features_001': (all_results['P_value'] < 0.001).sum(),
        'significant_features_01': (all_results['P_value'] < 0.01).sum(),
        'significant_features_05': (all_results['P_value'] < 0.05).sum(),
        'percentage_significant_05': (all_results['P_value'] < 0.05).mean() * 100,
        'large_effect_features': (all_results['Eta_squared'] >= 0.14).sum(),
        'medium_effect_features': ((all_results['Eta_squared'] >= 0.06) & (all_results['Eta_squared'] < 0.14)).sum(),
        'small_effect_features': ((all_results['Eta_squared'] >= 0.01) & (all_results['Eta_squared'] < 0.06)).sum()
    }

    # 保存汇总统计
    summary_df = pd.DataFrame([summary_stats])
    summary_df.to_csv(os.path.join(output_dir, '判别性汇总统计_固定K.csv'), index=False)

    # 按Phase分组的统计
    phase_stats = all_results.groupby('Phase').agg({
        'F_statistic': ['mean', 'max', 'std'],
        'Eta_squared': ['mean', 'max', 'std'],
        'Cohen_d': ['mean', 'max', 'std'],
        'P_value': lambda x: (x < 0.05).sum()
    }).round(4)

    phase_stats.to_csv(os.path.join(output_dir, '按阶段判别性统计_固定K.csv'))

    # 按Pattern分组的统计
    pattern_stats = all_results.groupby('Pattern').agg({
        'F_statistic': ['mean', 'max', 'std'],
        'Eta_squared': ['mean', 'max', 'std'],
        'Cohen_d': ['mean', 'max', 'std'],
        'P_value': lambda x: (x < 0.05).sum()
    }).round(4)

    pattern_stats.to_csv(os.path.join(output_dir, '按模式判别性统计_固定K.csv'))

    # 按Channel分组的统计
    channel_stats = all_results.groupby('Channel').agg({
        'F_statistic': ['mean', 'max', 'std'],
        'Eta_squared': ['mean', 'max', 'std'],
        'Cohen_d': ['mean', 'max', 'std'],
        'P_value': lambda x: (x < 0.05).sum()
    }).round(4)

    channel_stats.to_csv(os.path.join(output_dir, '按通道判别性统计_固定K.csv'))

    print(f"  已保存: 判别性汇总统计_固定K.csv")
    print(f"  已保存: 按阶段判别性统计_固定K.csv")
    print(f"  已保存: 按模式判别性统计_固定K.csv")
    print(f"  已保存: 按通道判别性统计_固定K.csv")

def create_comprehensive_report(all_results, output_dir):
    """
    创建综合分析报告
    """
    if all_results.empty:
        return

    print("创建综合分析报告...")

    report_file = os.path.join(output_dir, '三类人群判别性分析报告.txt')

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("=== 三类人群肌肉协同模式判别性分析报告 ===\n\n")

        # 基本信息
        f.write("1. 基本信息\n")
        f.write("-" * 50 + "\n")
        f.write(f"总特征数: {len(all_results)}\n")
        f.write(f"时间阶段数: {all_results['Phase'].nunique()}\n")
        f.write(f"聚类模式数: {all_results['Pattern'].nunique()}\n")
        f.write(f"EMG通道数: {all_results['Channel'].nunique()}\n")
        f.write(f"人群数: {all_results['N_groups'].iloc[0] if len(all_results) > 0 else 'N/A'}\n\n")

        # 显著性统计
        f.write("2. 显著性统计\n")
        f.write("-" * 50 + "\n")
        sig_001 = (all_results['P_value'] < 0.001).sum()
        sig_01 = (all_results['P_value'] < 0.01).sum()
        sig_05 = (all_results['P_value'] < 0.05).sum()
        total = len(all_results)

        f.write(f"p < 0.001: {sig_001} ({sig_001/total*100:.1f}%)\n")
        f.write(f"p < 0.01: {sig_01} ({sig_01/total*100:.1f}%)\n")
        f.write(f"p < 0.05: {sig_05} ({sig_05/total*100:.1f}%)\n")
        f.write(f"p ≥ 0.05: {total-sig_05} ({(total-sig_05)/total*100:.1f}%)\n\n")

        # 效应量统计
        f.write("3. 效应量统计 (η²)\n")
        f.write("-" * 50 + "\n")
        large_effect = (all_results['Eta_squared'] >= 0.14).sum()
        medium_effect = ((all_results['Eta_squared'] >= 0.06) & (all_results['Eta_squared'] < 0.14)).sum()
        small_effect = ((all_results['Eta_squared'] >= 0.01) & (all_results['Eta_squared'] < 0.06)).sum()

        f.write(f"大效应 (≥0.14): {large_effect} ({large_effect/total*100:.1f}%)\n")
        f.write(f"中等效应 (0.06-0.14): {medium_effect} ({medium_effect/total*100:.1f}%)\n")
        f.write(f"小效应 (0.01-0.06): {small_effect} ({small_effect/total*100:.1f}%)\n")
        f.write(f"无效应 (<0.01): {total-large_effect-medium_effect-small_effect} ({(total-large_effect-medium_effect-small_effect)/total*100:.1f}%)\n\n")

        # 各阶段表现
        f.write("4. 各阶段判别性表现\n")
        f.write("-" * 50 + "\n")
        phase_summary = all_results.groupby('Phase').agg({
            'F_statistic': 'mean',
            'Eta_squared': 'mean',
            'P_value': lambda x: (x < 0.05).sum()
        }).round(4)

        f.write(f"{'阶段':<8} {'平均F值':<12} {'平均η²':<12} {'显著特征数':<12}\n")
        f.write("-" * 50 + "\n")
        for phase, row in phase_summary.iterrows():
            f.write(f"{phase:<8} {row['F_statistic']:<12.4f} {row['Eta_squared']:<12.4f} {row['P_value']:<12}\n")

        f.write("\n5. 最具判别性的特征 (前10)\n")
        f.write("-" * 50 + "\n")
        top_features = all_results.nlargest(10, 'F_statistic')
        f.write(f"{'排名':<4} {'特征ID':<25} {'F值':<10} {'η²':<10} {'P值':<10}\n")
        f.write("-" * 70 + "\n")
        for i, (_, row) in enumerate(top_features.iterrows(), 1):
            feature_id = f"{row['Side']}_{row['Phase']}_{row['Pattern']}_{row['Channel']}"
            f.write(f"{i:<4} {feature_id:<25} {row['F_statistic']:<10.4f} {row['Eta_squared']:<10.4f} {row['P_value']:<10.4f}\n")

        f.write("\n" + "=" * 70 + "\n")
        f.write("分析完成！\n")
        f.write("详细结果请查看相应的CSV文件和可视化图表。\n")

    print(f"  已保存: 三类人群判别性分析报告.txt")

if __name__ == "__main__":
    print("=== 三类人群判别性分析 ===")
    
    # 设置路径
    BASE_DIR = r"D:\论文\中期\第四章\数据\模式\phase_separated_clustering_results_fixed_k_optimized\L_final"
    OUTPUT_DIR = os.path.join(BASE_DIR, "discriminative_score_analysis_fixed_k")
    
    print(f"输入目录: {BASE_DIR}")
    print(f"输出目录: {OUTPUT_DIR}")
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 检查输入目录是否存在
    if not os.path.exists(BASE_DIR):
        print(f"错误: 输入目录不存在: {BASE_DIR}")
        exit()
    
    # 加载数据
    data_dict = load_fixed_k_results(BASE_DIR)
    
    if not data_dict:
        print("未加载到数据。退出。")
        exit()
    
    print(f"\n成功加载 {len(data_dict)} 个CSV文件!")
    
    all_results = []
    
    # 处理每个文件
    for file_name, df in data_dict.items():
        print(f"\n处理 {file_name}...")
        
        # 解析文件名获取Phase信息
        side, phase = parse_fixed_k_filename(file_name)
        print(f"  解析: Side={side}, Phase={phase}")
        
        # 计算discriminative scores
        file_results = calculate_discriminative_scores_fixed_k(df, (side, phase))
        
        if not file_results.empty:
            all_results.append(file_results)
            print(f"  计算了 {len(file_results)} 个判别性分数")
        else:
            print(f"  {file_name} 没有有效结果")
    
    if not all_results:
        print("没有结果可分析。退出。")
        exit()
    
    # 合并所有结果
    combined_results = pd.concat(all_results, ignore_index=True)
    print(f"\n总计算的判别性分数: {len(combined_results)}")
    
    # 保存详细结果
    combined_results.to_csv(os.path.join(OUTPUT_DIR, '判别性分数详细结果_固定K.csv'), index=False)
    print(f"已保存: 判别性分数详细结果_固定K.csv")
    
    # 创建热图
    create_discriminative_heatmaps_fixed_k(combined_results, OUTPUT_DIR)

    # 创建top特征图
    create_top_discriminative_features_fixed_k(combined_results, OUTPUT_DIR, top_n=20)

    # 创建阶段比较分析
    create_phase_comparison_analysis(combined_results, OUTPUT_DIR)

    # 创建人群比较分析
    create_population_comparison_analysis(combined_results, OUTPUT_DIR)

    # 创建汇总统计
    create_summary_statistics_fixed_k(combined_results, OUTPUT_DIR)

    # 创建综合报告
    create_comprehensive_report(combined_results, OUTPUT_DIR)

    print(f"\n=== 分析完成 ===")
    print(f"所有结果保存到: {OUTPUT_DIR}")
    print(f"主要输出文件:")
    print(f"  - 判别性分数详细结果_固定K.csv: 完整结果")
    print(f"  - 判别性热图_*_固定K.png: 不同指标的热图")
    print(f"  - 前20判别性特征_*_固定K.png: 前20个判别性特征")
    print(f"  - 阶段比较分析.png: 阶段比较分析")
    print(f"  - 人群比较分析.png: 人群比较分析")
    print(f"  - 判别性汇总统计_固定K.csv: 汇总统计")
    print(f"  - 三类人群判别性分析报告.txt: 综合分析报告")

    # 显示关键统计信息
    print(f"\n=== 关键统计信息 ===")
    print(f"总特征数: {len(combined_results)}")
    print(f"显著特征数 (p<0.05): {(combined_results['P_value'] < 0.05).sum()}")
    print(f"显著比例: {(combined_results['P_value'] < 0.05).mean()*100:.1f}%")
    print(f"平均F统计量: {combined_results['F_statistic'].mean():.4f}")
    print(f"平均η²效应量: {combined_results['Eta_squared'].mean():.4f}")
    print(f"大效应特征数 (η²≥0.14): {(combined_results['Eta_squared'] >= 0.14).sum()}")

    # 显示最佳判别性特征
    print(f"\n=== 最佳判别性特征 (前5) ===")
    top_5 = combined_results.nlargest(5, 'F_statistic')
    for i, (_, row) in enumerate(top_5.iterrows(), 1):
        feature_id = f"{row['Side']}_{row['Phase']}_{row['Pattern']}_{row['Channel']}"
        print(f"{i}. {feature_id}: F={row['F_statistic']:.4f}, η²={row['Eta_squared']:.4f}, p={row['P_value']:.4f}")
