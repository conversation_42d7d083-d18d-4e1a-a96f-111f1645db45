import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import itertools
from pathlib import Path
import glob
from scipy import stats
from sklearn.utils import resample
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_preprocess_data(base_dir):
    """
    加载并预处理数据，确保人群分离 - 适用于三类人群
    """
    print(f"从目录加载数据: {base_dir}")
    
    csv_files = glob.glob(os.path.join(base_dir, "*.csv"))
    
    if not csv_files:
        print("未找到CSV文件!")
        return {}
    
    all_phase_data = {}
    
    for file_path in csv_files:
        file_name = os.path.basename(file_path)
        phase_id = file_name.replace('.csv', '')
        
        print(f"  加载: {file_name} -> 阶段: {phase_id}")
        
        try:
            df = pd.read_csv(file_path)
            
            # 检查必要的列
            required_cols = ['File_ID', 'Synergy_Index', 'Cluster_Label', 'population_label']
            emg_cols = [f'EMG{i}' for i in range(1, 9)]
            required_cols.extend(emg_cols)
            
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"    警告: 缺少列 {missing_cols}")
                continue
            
            # 数据预处理
            df = preprocess_phase_data(df, phase_id)
            
            print(f"    形状: {df.shape}")
            print(f"    人群: {sorted(df['population_label'].unique())}")
            print(f"    受试者: {len(df['subject_id'].unique())}")
            print(f"    步态周期: {len(df['step_cycle_id'].unique())}")
            
            all_phase_data[phase_id] = df
            
        except Exception as e:
            print(f"    加载 {file_name} 时出错: {e}")
    
    return all_phase_data

def preprocess_phase_data(df, phase_id):
    """
    预处理阶段数据，提取被试和步态周期信息 - 适用于三类人群
    """
    # 从File_ID提取被试和步态周期信息
    df['subject_id'] = df['File_ID'].str.extract(r'([^_]+(?:_[^_]+)*?)_L_gait')[0]
    df['step_cycle_id'] = df['File_ID'].str.extract(r'cycle_(\d+)')[0]
    
    # 创建唯一标识符，包含人群信息以区分三类人群
    df['unique_subject_id'] = df['subject_id'] + '_' + df['population_label'].astype(str)
    df['unique_step_cycle_id'] = df['subject_id'] + '_' + df['step_cycle_id'].astype(str) + '_' + df['population_label'].astype(str)
    
    return df

def create_unordered_pattern(ordered_pattern):
    """
    创建无序模式（排序后的模式）
    """
    return tuple(sorted(ordered_pattern))

def create_element_based_pattern(ordered_pattern):
    """
    创建基于元素的模式组合（去重后排序的元组）
    例如：(2,2) -> (2,), (2,2,3) -> (2,3), (1,3,1) -> (1,3)
    """
    return tuple(sorted(set(ordered_pattern)))

def extract_step_level_patterns(all_phase_data):
    """
    在步级层面提取每个步态周期下各阶段的协同模式组合
    """
    print("\n=== 提取步级模式组合 ===")
    
    step_patterns = defaultdict(dict)
    
    # 收集所有步态周期
    all_step_cycles = set()
    for df in all_phase_data.values():
        all_step_cycles.update(df['unique_step_cycle_id'].unique())
    
    print(f"总唯一步态周期数: {len(all_step_cycles)}")
    
    # 为每个步态周期提取各阶段的模式组合
    for step_cycle in all_step_cycles:
        step_patterns[step_cycle] = {}
        
        # 收集该步态周期在各阶段的数据
        for phase_id, df in all_phase_data.items():
            step_data = df[df['unique_step_cycle_id'] == step_cycle]
            
            if len(step_data) > 0:
                # 获取该阶段该步态周期的协同模式组合
                synergy_clusters = {}
                for _, row in step_data.iterrows():
                    synergy_idx = row['Synergy_Index']
                    cluster_label = row['Cluster_Label']
                    synergy_clusters[synergy_idx] = cluster_label
                
                # 创建三种模式组合
                if synergy_clusters:
                    sorted_synergies = sorted(synergy_clusters.keys())
                    ordered_pattern = tuple(synergy_clusters[syn] for syn in sorted_synergies)
                    unordered_pattern = create_unordered_pattern(ordered_pattern)
                    element_pattern = create_element_based_pattern(ordered_pattern)
                    
                    step_patterns[step_cycle][phase_id] = {
                        'ordered_pattern': ordered_pattern,
                        'unordered_pattern': unordered_pattern,
                        'element_pattern': element_pattern,
                        'synergy_mapping': synergy_clusters,
                        'subject_id': step_data['unique_subject_id'].iloc[0],
                        'population': step_data['population_label'].iloc[0]
                    }
    
    return step_patterns

def aggregate_to_individual_level(step_patterns):
    """
    从步级聚合到个体层面，统计各阶段的常见组合模式
    """
    print("\n=== 聚合到个体层面 ===")
    
    individual_ordered_patterns = defaultdict(lambda: defaultdict(list))
    individual_unordered_patterns = defaultdict(lambda: defaultdict(list))
    individual_element_patterns = defaultdict(lambda: defaultdict(list))
    
    # 按被试和阶段聚合
    for step_cycle, phases_data in step_patterns.items():
        if not phases_data:
            continue
            
        # 获取被试信息
        subject_id = None
        population = None
        for phase_data in phases_data.values():
            if subject_id is None:
                subject_id = phase_data['subject_id']
                population = phase_data['population']
            break
        
        if subject_id is None:
            continue
        
        # 为每个阶段记录该被试的模式组合
        for phase_id, phase_data in phases_data.items():
            ordered_pattern = phase_data['ordered_pattern']
            unordered_pattern = phase_data['unordered_pattern']
            element_pattern = phase_data['element_pattern']
            individual_ordered_patterns[subject_id][phase_id].append(ordered_pattern)
            individual_unordered_patterns[subject_id][phase_id].append(unordered_pattern)
            individual_element_patterns[subject_id][phase_id].append(element_pattern)
    
    # 计算每个被试在每个阶段的最常见模式
    individual_common_patterns = {}
    
    for subject_id in individual_ordered_patterns.keys():
        individual_common_patterns[subject_id] = {}
        
        for phase_id in individual_ordered_patterns[subject_id].keys():
            ordered_patterns = individual_ordered_patterns[subject_id][phase_id]
            unordered_patterns = individual_unordered_patterns[subject_id][phase_id]
            element_patterns = individual_element_patterns[subject_id][phase_id]
            
            if ordered_patterns:
                # 统计各种模式的频率
                ordered_counts = Counter(ordered_patterns)
                unordered_counts = Counter(unordered_patterns)
                element_counts = Counter(element_patterns)
                
                most_common_ordered = ordered_counts.most_common(1)[0]
                most_common_unordered = unordered_counts.most_common(1)[0]
                most_common_element = element_counts.most_common(1)[0]
                
                individual_common_patterns[subject_id][phase_id] = {
                    'ordered_pattern': {
                        'most_common': most_common_ordered[0],
                        'frequency': most_common_ordered[1],
                        'diversity': len(ordered_counts),
                        'all_patterns': dict(ordered_counts)
                    },
                    'unordered_pattern': {
                        'most_common': most_common_unordered[0],
                        'frequency': most_common_unordered[1],
                        'diversity': len(unordered_counts),
                        'all_patterns': dict(unordered_counts)
                    },
                    'element_pattern': {
                        'most_common': most_common_element[0],
                        'frequency': most_common_element[1],
                        'diversity': len(element_counts),
                        'all_patterns': dict(element_counts)
                    },
                    'total_steps': len(ordered_patterns)
                }
    
    return individual_common_patterns

def bootstrap_confidence_interval(data, n_bootstrap=1000, confidence=0.95):
    """
    计算bootstrap置信区间
    """
    if len(data) == 0:
        return 0, 0, 0
    
    bootstrap_proportions = []
    n_total = len(data)
    
    for _ in range(n_bootstrap):
        bootstrap_sample = resample(data, n_samples=n_total, random_state=None)
        # 计算最常见模式的比例
        pattern_counts = Counter(bootstrap_sample)
        if pattern_counts:
            most_common_count = pattern_counts.most_common(1)[0][1]
            proportion = most_common_count / n_total
            bootstrap_proportions.append(proportion)
    
    if not bootstrap_proportions:
        return 0, 0, 0
    
    alpha = 1 - confidence
    lower_percentile = (alpha / 2) * 100
    upper_percentile = (1 - alpha / 2) * 100
    
    ci_lower = np.percentile(bootstrap_proportions, lower_percentile)
    ci_upper = np.percentile(bootstrap_proportions, upper_percentile)
    mean_proportion = np.mean(bootstrap_proportions)
    
    return mean_proportion, ci_lower, ci_upper

def analyze_population_patterns_with_bootstrap(individual_patterns, all_phase_data):
    """
    分析群体层面的高频组合模式，包含bootstrap CI - 适用于三类人群
    """
    print("\n=== 分析群体层面模式（包含Bootstrap CI）===")

    # 获取被试的人群信息
    subject_population_map = {}
    for df in all_phase_data.values():
        for _, row in df.iterrows():
            subject_id = row['unique_subject_id']
            population = row['population_label']
            subject_population_map[subject_id] = population

    # 按人群和阶段统计模式
    population_ordered_patterns = defaultdict(lambda: defaultdict(list))
    population_unordered_patterns = defaultdict(lambda: defaultdict(list))
    population_element_patterns = defaultdict(lambda: defaultdict(list))

    # 同时收集受试层面的详细信息
    subject_pattern_details = defaultdict(lambda: defaultdict(list))

    for subject_id, subject_phases in individual_patterns.items():
        population = subject_population_map.get(subject_id, 'Unknown')

        for phase_id, phase_data in subject_phases.items():
            ordered_pattern = phase_data['ordered_pattern']['most_common']
            unordered_pattern = phase_data['unordered_pattern']['most_common']
            element_pattern = phase_data['element_pattern']['most_common']

            population_ordered_patterns[population][phase_id].append(ordered_pattern)
            population_unordered_patterns[population][phase_id].append(unordered_pattern)
            population_element_patterns[population][phase_id].append(element_pattern)

            # 保存受试详细信息
            subject_pattern_details[population][phase_id].append({
                'subject_id': subject_id,
                'ordered_pattern': ordered_pattern,
                'unordered_pattern': unordered_pattern,
                'element_pattern': element_pattern,
                'ordered_frequency': phase_data['ordered_pattern']['frequency'],
                'unordered_frequency': phase_data['unordered_pattern']['frequency'],
                'element_frequency': phase_data['element_pattern']['frequency'],
                'ordered_consistency': phase_data['ordered_pattern']['frequency'] / phase_data['total_steps'],
                'unordered_consistency': phase_data['unordered_pattern']['frequency'] / phase_data['total_steps'],
                'element_consistency': phase_data['element_pattern']['frequency'] / phase_data['total_steps'],
                'total_steps': phase_data['total_steps'],
                'ordered_diversity': phase_data['ordered_pattern']['diversity'],
                'unordered_diversity': phase_data['unordered_pattern']['diversity'],
                'element_diversity': phase_data['element_pattern']['diversity']
            })

    # 计算每个人群每个阶段的高频模式（重点关注元素模式的bootstrap CI）
    population_common_patterns = {}

    for population in population_ordered_patterns.keys():
        population_common_patterns[population] = {}

        for phase_id in population_ordered_patterns[population].keys():
            ordered_patterns = population_ordered_patterns[population][phase_id]
            unordered_patterns = population_unordered_patterns[population][phase_id]
            element_patterns = population_element_patterns[population][phase_id]

            # 计算各种模式的频率和bootstrap CI
            def analyze_pattern_type(patterns, pattern_name):
                if not patterns:
                    return {'top_patterns': [], 'diversity': 0}

                pattern_counts = Counter(patterns)
                total_subjects = len(patterns)

                # 获取前5个最常见的模式
                top_patterns = []
                for pattern, count in pattern_counts.most_common(5):
                    frequency = count / total_subjects

                    # 为该特定模式计算bootstrap CI
                    pattern_binary = [1 if p == pattern else 0 for p in patterns]
                    mean_prop, ci_lower, ci_upper = bootstrap_confidence_interval(
                        pattern_binary, n_bootstrap=1000)

                    top_patterns.append({
                        'pattern': pattern,
                        'count': count,
                        'frequency': frequency,
                        'bootstrap_proportion': mean_prop,
                        'ci_lower': ci_lower,
                        'ci_upper': ci_upper,
                        'ci_width': ci_upper - ci_lower
                    })

                return {
                    'top_patterns': top_patterns,
                    'diversity': len(pattern_counts)
                }

            population_common_patterns[population][phase_id] = {
                'ordered_patterns': analyze_pattern_type(ordered_patterns, 'ordered'),
                'unordered_patterns': analyze_pattern_type(unordered_patterns, 'unordered'),
                'element_patterns': analyze_pattern_type(element_patterns, 'element'),
                'total_subjects': len(ordered_patterns),
                'subject_details': subject_pattern_details[population][phase_id]
            }

    return population_common_patterns

def prepare_subject_level_data_for_comparison(population_patterns):
    """
    准备受试层面的数据用于组间比较 - 适用于三类人群
    """
    print("\n=== 准备受试层面数据用于组间比较 ===")

    subject_data_for_comparison = []

    for population, pop_phases in population_patterns.items():
        for phase_id, phase_data in pop_phases.items():
            for subject_detail in phase_data['subject_details']:
                subject_data_for_comparison.append({
                    'subject_id': subject_detail['subject_id'],
                    'population': population,
                    'phase_id': phase_id,
                    'element_pattern': str(subject_detail['element_pattern']),
                    'element_frequency': subject_detail['element_frequency'],
                    'element_consistency': subject_detail['element_consistency'],
                    'element_diversity': subject_detail['element_diversity'],
                    'total_steps': subject_detail['total_steps'],
                    'ordered_pattern': str(subject_detail['ordered_pattern']),
                    'unordered_pattern': str(subject_detail['unordered_pattern']),
                    'ordered_consistency': subject_detail['ordered_consistency'],
                    'unordered_consistency': subject_detail['unordered_consistency']
                })

    return pd.DataFrame(subject_data_for_comparison)

def identify_high_frequency_patterns_for_testing(population_patterns, min_frequency=0.2):
    """
    识别高频模式用于组间比较测试 - 适用于三类人群
    """
    print(f"\n=== 识别高频模式 (最小频率={min_frequency}) ===")

    patterns_for_testing = []

    for population, pop_phases in population_patterns.items():
        for phase_id, phase_data in pop_phases.items():
            for pattern_info in phase_data['element_patterns']['top_patterns']:
                if pattern_info['frequency'] >= min_frequency:
                    patterns_for_testing.append({
                        'population': population,
                        'phase_id': phase_id,
                        'pattern': pattern_info['pattern'],
                        'frequency': pattern_info['frequency'],
                        'count': pattern_info['count'],
                        'total_subjects': phase_data['total_subjects'],
                        'ci_lower': pattern_info['ci_lower'],
                        'ci_upper': pattern_info['ci_upper'],
                        'ci_width': pattern_info['ci_width']
                    })

    patterns_df = pd.DataFrame(patterns_for_testing)
    print(f"找到 {len(patterns_df)} 个高频模式用于测试")

    return patterns_df

def perform_group_comparisons_permutation(subject_data_df, high_freq_patterns_df, n_permutations=10000):
    """
    使用置换检验进行组间比较 - 适用于三类人群
    """
    print(f"\n=== 执行组间比较（置换检验，n={n_permutations}）===")

    if high_freq_patterns_df.empty:
        print("没有高频模式可测试")
        return pd.DataFrame()

    # 获取需要测试的唯一组合
    unique_combinations = high_freq_patterns_df[['phase_id', 'pattern']].drop_duplicates()

    comparison_results = []

    for _, combo in unique_combinations.iterrows():
        phase_id = combo['phase_id']
        pattern = combo['pattern']

        print(f"  测试 {phase_id} - {pattern}")

        # 获取该阶段的所有受试数据
        phase_data = subject_data_df[subject_data_df['phase_id'] == phase_id].copy()

        if len(phase_data) == 0:
            continue

        # 创建二进制变量：是否使用该元素模式
        phase_data['uses_pattern'] = (phase_data['element_pattern'] == str(pattern)).astype(int)

        # 按人群分组
        groups = {}
        for pop in phase_data['population'].unique():
            pop_data = phase_data[phase_data['population'] == pop]
            groups[pop] = pop_data['uses_pattern'].values

        # 进行两两比较（三类人群：0vs1, 0vs2, 1vs2）
        population_pairs = list(itertools.combinations(groups.keys(), 2))

        for pop1, pop2 in population_pairs:
            group1 = groups[pop1]
            group2 = groups[pop2]

            if len(group1) == 0 or len(group2) == 0:
                continue

            # 观察到的差异
            obs_diff = np.mean(group1) - np.mean(group2)

            # 置换检验
            combined = np.concatenate([group1, group2])
            n1 = len(group1)

            perm_diffs = []
            for _ in range(n_permutations):
                np.random.shuffle(combined)
                perm_group1 = combined[:n1]
                perm_group2 = combined[n1:]
                perm_diff = np.mean(perm_group1) - np.mean(perm_group2)
                perm_diffs.append(perm_diff)

            # 计算p值
            p_value = np.mean(np.abs(perm_diffs) >= np.abs(obs_diff))

            # 计算效应量（Cohen's h for proportions）
            p1, p2 = np.mean(group1), np.mean(group2)
            cohens_h = 2 * (np.arcsin(np.sqrt(p1)) - np.arcsin(np.sqrt(p2)))

            # 人群名称映射
            pop_names = {0: '健康人群', 1: '早期PD', 2: '中期PD'}
            pop1_name = pop_names.get(pop1, f'人群{pop1}')
            pop2_name = pop_names.get(pop2, f'人群{pop2}')

            comparison_results.append({
                'phase_id': phase_id,
                'pattern': str(pattern),
                'group1': pop1,
                'group2': pop2,
                'group1_name': pop1_name,
                'group2_name': pop2_name,
                'n1': len(group1),
                'n2': len(group2),
                'prop1': p1,
                'prop2': p2,
                'observed_diff': obs_diff,
                'cohens_h': cohens_h,
                'p_value': p_value,
                'comparison': f"{pop1_name} vs {pop2_name}"
            })

    return pd.DataFrame(comparison_results)

def apply_multiple_comparison_correction(comparison_results_df, alpha=0.05):
    """
    应用多重比较校正
    """
    print(f"\n=== 应用多重比较校正 (alpha={alpha}) ===")

    if comparison_results_df.empty:
        return comparison_results_df, pd.DataFrame()

    # Benjamini-Hochberg FDR校正
    p_values = comparison_results_df['p_value'].values
    n_tests = len(p_values)

    # 排序p值
    sorted_indices = np.argsort(p_values)
    sorted_p_values = p_values[sorted_indices]

    # 计算校正后的p值
    adjusted_p_values = np.zeros(n_tests)
    for i in range(n_tests):
        bh_value = sorted_p_values[i] * n_tests / (i + 1)
        adjusted_p_values[sorted_indices[i]] = min(bh_value, 1.0)

    # 确保单调性
    for i in range(n_tests - 2, -1, -1):
        if adjusted_p_values[sorted_indices[i]] > adjusted_p_values[sorted_indices[i + 1]]:
            adjusted_p_values[sorted_indices[i]] = adjusted_p_values[sorted_indices[i + 1]]

    comparison_results_df['p_adjusted'] = adjusted_p_values
    comparison_results_df['significant'] = adjusted_p_values < alpha

    # 提取显著性结果
    significant_results = comparison_results_df[comparison_results_df['significant']].copy()

    print(f"总比较次数: {len(comparison_results_df)}")
    print(f"校正后显著: {len(significant_results)}")

    return comparison_results_df, significant_results

def save_enhanced_results(step_patterns, individual_patterns, population_patterns,
                         subject_data_df, high_freq_patterns_df, comparison_results_df,
                         significant_results_df, all_phase_data, output_dir):
    """
    保存增强的分析结果 - 适用于三类人群
    """
    print("\n=== 保存增强结果 ===")

    # 1. 原有的基础结果
    step_results = []
    for step_cycle, phases_data in step_patterns.items():
        for phase_id, phase_data in phases_data.items():
            step_results.append({
                'step_cycle_id': step_cycle,
                'phase_id': phase_id,
                'subject_id': phase_data['subject_id'],
                'population': phase_data['population'],
                'ordered_pattern': str(phase_data['ordered_pattern']),
                'unordered_pattern': str(phase_data['unordered_pattern']),
                'element_pattern': str(phase_data['element_pattern']),
                'synergy_mapping': str(phase_data['synergy_mapping'])
            })

    step_df = pd.DataFrame(step_results)
    step_df.to_excel(os.path.join(output_dir, '步级模式.xlsx'), index=False)

    # 2. 个体层面结果
    individual_results = []
    for subject_id, subject_phases in individual_patterns.items():
        for phase_id, phase_data in subject_phases.items():
            individual_results.append({
                'subject_id': subject_id,
                'phase_id': phase_id,
                'ordered_most_common': str(phase_data['ordered_pattern']['most_common']),
                'ordered_frequency': phase_data['ordered_pattern']['frequency'],
                'ordered_diversity': phase_data['ordered_pattern']['diversity'],
                'unordered_most_common': str(phase_data['unordered_pattern']['most_common']),
                'unordered_frequency': phase_data['unordered_pattern']['frequency'],
                'unordered_diversity': phase_data['unordered_pattern']['diversity'],
                'element_most_common': str(phase_data['element_pattern']['most_common']),
                'element_frequency': phase_data['element_pattern']['frequency'],
                'element_diversity': phase_data['element_pattern']['diversity'],
                'total_steps': phase_data['total_steps'],
                'ordered_consistency': phase_data['ordered_pattern']['frequency'] / phase_data['total_steps'],
                'unordered_consistency': phase_data['unordered_pattern']['frequency'] / phase_data['total_steps'],
                'element_consistency': phase_data['element_pattern']['frequency'] / phase_data['total_steps']
            })

    individual_df = pd.DataFrame(individual_results)
    individual_df.to_excel(os.path.join(output_dir, '个体层面模式.xlsx'), index=False)

    # 3. 群体层面结果（包含bootstrap CI）
    population_results = []
    for population, pop_phases in population_patterns.items():
        for phase_id, phase_data in pop_phases.items():
            # 元素模式（重点）
            for i, pattern_info in enumerate(phase_data['element_patterns']['top_patterns']):
                # 人群名称映射
                pop_names = {0: '健康人群', 1: '早期PD', 2: '中期PD'}
                pop_name = pop_names.get(population, f'人群{population}')

                population_results.append({
                    'population': population,
                    'population_name': pop_name,
                    'phase_id': phase_id,
                    'rank': i + 1,
                    'pattern_type': 'element',
                    'pattern_combination': str(pattern_info['pattern']),
                    'subject_count': pattern_info['count'],
                    'frequency': pattern_info['frequency'],
                    'bootstrap_proportion': pattern_info['bootstrap_proportion'],
                    'ci_lower': pattern_info['ci_lower'],
                    'ci_upper': pattern_info['ci_upper'],
                    'ci_width': pattern_info['ci_width'],
                    'total_subjects': phase_data['total_subjects']
                })

    population_df = pd.DataFrame(population_results)
    population_df.to_excel(os.path.join(output_dir, '群体层面模式_含CI.xlsx'), index=False)

    # 4. 受试层面数据（用于组间比较）
    subject_data_df.to_excel(os.path.join(output_dir, '受试层面数据_用于比较.xlsx'), index=False)

    # 5. 高频模式列表
    high_freq_patterns_df.to_excel(os.path.join(output_dir, '高频模式.xlsx'), index=False)

    # 6. 组间比较结果
    if not comparison_results_df.empty:
        comparison_results_df.to_excel(os.path.join(output_dir, '组间比较结果.xlsx'), index=False)

    # 7. 显著性结果
    if not significant_results_df.empty:
        significant_results_df.to_excel(os.path.join(output_dir, '显著组间差异.xlsx'), index=False)

    print("  已保存所有增强结果")

    return (step_df, individual_df, population_df, subject_data_df,
            high_freq_patterns_df, comparison_results_df, significant_results_df)

def create_pattern_visualizations(population_patterns, output_dir):
    """
    创建模式组合的可视化（有序、无序、基于元素）- 适用于三类人群
    """
    print("\n=== 创建可视化 ===")

    populations = list(population_patterns.keys())
    phases = set()
    for pop_phases in population_patterns.values():
        phases.update(pop_phases.keys())
    phases = sorted(phases)

    # 人群名称映射
    pop_names = {0: '健康人群', 1: '早期PD', 2: '中期PD'}
    pop_labels = [pop_names.get(pop, f'人群{pop}') for pop in populations]

    # 1. 三种模式频率对比热图
    fig, axes = plt.subplots(1, 3, figsize=(20, 6))

    pattern_types = ['ordered', 'unordered', 'element']
    pattern_labels = ['有序模式', '无序模式', '基于元素模式']

    for idx, (pattern_type, label) in enumerate(zip(pattern_types, pattern_labels)):
        freq_matrix = np.zeros((len(populations), len(phases)))

        for i, population in enumerate(populations):
            for j, phase in enumerate(phases):
                if phase in population_patterns[population]:
                    pattern_key = f'{pattern_type}_patterns'
                    if population_patterns[population][phase][pattern_key]['top_patterns']:
                        top_pattern = population_patterns[population][phase][pattern_key]['top_patterns'][0]
                        freq_matrix[i, j] = top_pattern['frequency']

        sns.heatmap(freq_matrix,
                    xticklabels=phases,
                    yticklabels=pop_labels,
                    annot=True,
                    fmt='.2f',
                    cmap='YlOrRd',
                    cbar_kws={'label': '顶级模式频率'},
                    ax=axes[idx])
        axes[idx].set_title(f'{label}频率')
        axes[idx].set_xlabel('阶段')
        if idx == 0:
            axes[idx].set_ylabel('人群')

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, '三种模式频率对比.png'), dpi=300, bbox_inches='tight')
    plt.close()

    # 2. 模式多样性比较
    diversity_data = []
    for population, pop_phases in population_patterns.items():
        pop_name = pop_names.get(population, f'人群{population}')
        for phase, phase_data in pop_phases.items():
            diversity_data.append({
                'Population': pop_name,
                'Phase': phase,
                'Ordered_Diversity': phase_data['ordered_patterns']['diversity'],
                'Unordered_Diversity': phase_data['unordered_patterns']['diversity'],
                'Element_Diversity': phase_data['element_patterns']['diversity'],
                'Total_Subjects': phase_data['total_subjects']
            })

    diversity_df = pd.DataFrame(diversity_data)

    if not diversity_df.empty:
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        diversity_types = ['Ordered_Diversity', 'Unordered_Diversity', 'Element_Diversity']
        diversity_labels = ['有序', '无序', '基于元素']

        for idx, (div_type, label) in enumerate(zip(diversity_types, diversity_labels)):
            pivot_diversity = diversity_df.pivot(index='Population', columns='Phase', values=div_type)
            sns.heatmap(pivot_diversity, annot=True, fmt='d', cmap='viridis',
                        cbar_kws={'label': '模式多样性'}, ax=axes[idx])
            axes[idx].set_title(f'{label}模式多样性')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, '模式多样性比较.png'), dpi=300, bbox_inches='tight')
        plt.close()

    # 3. 三类人群元素模式频率对比
    fig, ax = plt.subplots(figsize=(12, 8))

    # 收集所有元素模式
    all_element_patterns = set()
    for pop_phases in population_patterns.values():
        for phase_data in pop_phases.values():
            for pattern_info in phase_data['element_patterns']['top_patterns']:
                all_element_patterns.add(str(pattern_info['pattern']))

    # 创建模式频率矩阵
    pattern_freq_data = []
    for population in populations:
        pop_name = pop_names.get(population, f'人群{population}')
        for phase in phases:
            if phase in population_patterns[population]:
                phase_data = population_patterns[population][phase]
                for pattern_info in phase_data['element_patterns']['top_patterns'][:3]:  # 前3个模式
                    pattern_freq_data.append({
                        'Population': pop_name,
                        'Phase': phase,
                        'Pattern': str(pattern_info['pattern']),
                        'Frequency': pattern_info['frequency'],
                        'CI_Lower': pattern_info['ci_lower'],
                        'CI_Upper': pattern_info['ci_upper']
                    })

    if pattern_freq_data:
        pattern_freq_df = pd.DataFrame(pattern_freq_data)

        # 创建分组条形图
        phases_unique = sorted(pattern_freq_df['Phase'].unique())
        x = np.arange(len(phases_unique))
        width = 0.25

        populations_unique = sorted(pattern_freq_df['Population'].unique())
        colors = ['skyblue', 'lightgreen', 'lightcoral']

        for i, pop in enumerate(populations_unique):
            pop_data = pattern_freq_df[pattern_freq_df['Population'] == pop]
            phase_means = []
            phase_errors = []

            for phase in phases_unique:
                phase_pop_data = pop_data[pop_data['Phase'] == phase]
                if not phase_pop_data.empty:
                    mean_freq = phase_pop_data['Frequency'].mean()
                    # 使用CI宽度作为误差条
                    ci_width = (phase_pop_data['CI_Upper'] - phase_pop_data['CI_Lower']).mean() / 2
                    phase_means.append(mean_freq)
                    phase_errors.append(ci_width)
                else:
                    phase_means.append(0)
                    phase_errors.append(0)

            ax.bar(x + i * width, phase_means, width,
                   label=pop, color=colors[i % len(colors)],
                   yerr=phase_errors, capsize=5, alpha=0.8)

        ax.set_xlabel('阶段')
        ax.set_ylabel('平均模式频率')
        ax.set_title('三类人群各阶段元素模式频率对比')
        ax.set_xticks(x + width)
        ax.set_xticklabels(phases_unique)
        ax.legend()
        ax.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, '三类人群元素模式频率对比.png'), dpi=300, bbox_inches='tight')
        plt.close()

    print("  已保存所有可视化图表")

if __name__ == "__main__":
    print("=== 三类人群协同模式组合分析 ===")
    
    # 设置路径
    BASE_DIR = r"D:\论文\中期\第四章\数据\模式\phase_separated_clustering_results_fixed_k_optimized\L_final"
    OUTPUT_DIR = os.path.join(BASE_DIR, "synergy_pattern_combination_analysis_enhanced")
    
    print(f"输入目录: {BASE_DIR}")
    print(f"输出目录: {OUTPUT_DIR}")
    
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 检查输入目录是否存在
    if not os.path.exists(BASE_DIR):
        print(f"错误: 输入目录不存在: {BASE_DIR}")
        exit()
    
    # 1. 加载和预处理数据
    all_phase_data = load_and_preprocess_data(BASE_DIR)
    if not all_phase_data:
        print("未加载到数据。退出。")
        exit()
    
    # 2. 提取步级模式组合
    step_patterns = extract_step_level_patterns(all_phase_data)
    
    # 3. 聚合到个体层面
    individual_patterns = aggregate_to_individual_level(step_patterns)

    # 4. 分析群体层面模式（包含bootstrap CI）
    population_patterns = analyze_population_patterns_with_bootstrap(individual_patterns, all_phase_data)

    # 5. 准备受试层面数据用于组间比较
    subject_data_df = prepare_subject_level_data_for_comparison(population_patterns)

    # 6. 识别高频模式
    high_freq_patterns_df = identify_high_frequency_patterns_for_testing(population_patterns)

    # 7. 进行组间比较（置换检验）
    comparison_results_df = pd.DataFrame()
    significant_results_df = pd.DataFrame()

    if not high_freq_patterns_df.empty:
        comparison_results_df = perform_group_comparisons_permutation(
            subject_data_df, high_freq_patterns_df)

        if not comparison_results_df.empty:
            # 8. 多重比较校正
            comparison_results_df, significant_results_df = apply_multiple_comparison_correction(
                comparison_results_df)

    # 9. 保存所有结果
    results = save_enhanced_results(
        step_patterns, individual_patterns, population_patterns,
        subject_data_df, high_freq_patterns_df, comparison_results_df,
        significant_results_df, all_phase_data, OUTPUT_DIR)

    # 10. 创建可视化
    create_pattern_visualizations(population_patterns, OUTPUT_DIR)

    # 11. 打印摘要统计
    print(f"\n=== 增强分析摘要 ===")
    print(f"分析的总步态周期数: {len(step_patterns)}")
    print(f"总受试者数: {len(individual_patterns)}")
    print(f"总人群数: {len(population_patterns)}")
    print(f"识别的高频模式数: {len(high_freq_patterns_df)}")
    print(f"执行的组间比较数: {len(comparison_results_df)}")
    print(f"发现的显著差异数: {len(significant_results_df)}")

    # 显示人群分布
    print(f"\n=== 人群分布 ===")
    pop_names = {0: '健康人群', 1: '早期PD', 2: '中期PD'}
    for pop_id in population_patterns.keys():
        pop_name = pop_names.get(pop_id, f'人群{pop_id}')
        total_subjects = 0
        for phase_data in population_patterns[pop_id].values():
            total_subjects = max(total_subjects, phase_data['total_subjects'])
        print(f"  {pop_name} (标签{pop_id}): {total_subjects} 名受试者")

    # 显示显著性结果摘要
    if not significant_results_df.empty:
        print(f"\n=== 显著组间差异 ===")
        for _, row in significant_results_df.iterrows():
            print(f"  {row['phase_id']} - {row['pattern']}: {row['comparison']} "
                  f"(p_adj={row['p_adjusted']:.4f}, h={row['cohens_h']:.3f})")
    else:
        print(f"\n=== 显著组间差异 ===")
        print("  未发现显著的组间差异")

    # 显示高频模式摘要
    if not high_freq_patterns_df.empty:
        print(f"\n=== 高频模式摘要 (前5) ===")
        top_patterns = high_freq_patterns_df.nlargest(5, 'frequency')
        for _, row in top_patterns.iterrows():
            pop_name = pop_names.get(row['population'], f"人群{row['population']}")
            print(f"  {pop_name} - {row['phase_id']} - {row['pattern']}: "
                  f"频率={row['frequency']:.3f} ({row['count']}/{row['total_subjects']})")

    print(f"\n结果保存到: {OUTPUT_DIR}")
    print(f"主要输出文件:")
    print(f"  - 步级模式.xlsx: 步态周期级别的模式组合")
    print(f"  - 个体层面模式.xlsx: 个体层面的常见模式")
    print(f"  - 群体层面模式_含CI.xlsx: 群体层面模式及置信区间")
    print(f"  - 高频模式.xlsx: 识别的高频模式列表")
    print(f"  - 组间比较结果.xlsx: 详细的组间比较结果")
    print(f"  - 显著组间差异.xlsx: 显著的组间差异")
    print(f"  - 三种模式频率对比.png: 三种模式类型的频率热图")
    print(f"  - 模式多样性比较.png: 模式多样性分析")
    print(f"  - 三类人群元素模式频率对比.png: 三类人群的元素模式对比")

    print(f"\n=== 分析完成 ===")
