import numpy as np
import pandas as pd
import os
import scipy.signal as signal

def get_angle(point_side1_1, point_side1_2, point_side1_3, point_side2_1, point_side2_2, point_side2_3):
    side_1_12 = np.linalg.norm(point_side1_1 - point_side1_2)
    side_1_23 = np.linalg.norm(point_side1_2 - point_side1_3)
    side_2_12 = np.linalg.norm(point_side2_1 - point_side2_2)
    side_2_23 = np.linalg.norm(point_side2_2 - point_side2_3)

    angel_1 = np.arccos(np.dot(point_side1_2 - point_side1_1, point_side1_2 - point_side1_3) / (side_1_12 * side_1_23))
    angel_2 = np.arccos(np.dot(point_side2_2 - point_side2_1, point_side2_2 - point_side2_3) / (side_2_12 * side_2_23))

    return angel_1, angel_2

def get_knee_angle(vicon_data):
    RTHI = vicon_data[[f"RTHI.{axis}" for axis in "XYZ"]].to_numpy()
    RKNE = vicon_data[[f"RKNE.{axis}" for axis in "XYZ"]].to_numpy()
    RTIB = vicon_data[[f"RTIB.{axis}" for axis in "XYZ"]].to_numpy()
    LTHI = vicon_data[[f"LTHI.{axis}" for axis in "XYZ"]].to_numpy()
    LKNE = vicon_data[[f"LKNE.{axis}" for axis in "XYZ"]].to_numpy()
    LTIB = vicon_data[[f"LTIB.{axis}" for axis in "XYZ"]].to_numpy()

    # 定义数组存储角度
    RKNEE_angle = np.zeros(RTHI.shape[0])
    LKNEE_angle = np.zeros(RTHI.shape[0])

    # 计算膝关节夹角 (三维角度)
    for i in range(RTHI.shape[0]):
        RKNEE_angle_i, LKNEE_angle_i = get_angle(RTHI[i], RKNE[i], RTIB[i], LTHI[i], LKNE[i], LTIB[i])
        # 存入数组
        RKNEE_angle[i] = RKNEE_angle_i
        LKNEE_angle[i] = LKNEE_angle_i

    return RKNEE_angle, LKNEE_angle

def get_ankle_angle(vicon_data):
    RTIB = vicon_data[[f"RTIB.{axis}" for axis in "XYZ"]].to_numpy()
    RANK = vicon_data[[f"RANK.{axis}" for axis in "XYZ"]].to_numpy()
    RTOE = vicon_data[[f"RTOE.{axis}" for axis in "XYZ"]].to_numpy()
    LTIB = vicon_data[[f"LTIB.{axis}" for axis in "XYZ"]].to_numpy()
    LANK = vicon_data[[f"LANK.{axis}" for axis in "XYZ"]].to_numpy()
    LTOE = vicon_data[[f"LTOE.{axis}" for axis in "XYZ"]].to_numpy()

    # 定义数组存储角度
    RANK_angle = np.zeros(RTIB.shape[0])
    LANK_angle = np.zeros(RTIB.shape[0])

    # 计算踝关节夹角 (三维角度)
    for i in range(RTIB.shape[0]):
        RANK_angle_i, LANK_angle_i = get_angle(RTIB[i], RANK[i], RTOE[i], LTIB[i], LANK[i], LTOE[i])
        # 存入数组
        RANK_angle[i] = RANK_angle_i
        LANK_angle[i] = LANK_angle_i

    return RANK_angle, LANK_angle

def butterworth_zero_phase(data, fs, cutoff=6, order=4):
    """
    Apply a zero-phase (forward-backward) Butterworth low-pass filter.
    """
    # 检查原始数据中是否缺值 有Nan值 若是在中间则根据插值补上 若是在最后则删除该行
    if np.any(np.isnan(data)):
        data = np.interp(np.arange(len(data)), np.arange(len(data))[~np.isnan(data)], data[~np.isnan(data)])

    nyq = 0.5 * fs
    norm_cutoff = cutoff / nyq
    b, a = signal.butter(order, norm_cutoff, btype='low', analog=False)
    
    return signal.filtfilt(b, a, data)

def get_joint_angle_speed(angle_data, frame = 100):
    angle_diff = np.diff(angle_data)
    angle_speed = angle_diff * (frame)
    return angle_speed

# 测试单个文件
test_file = 'D:/论文/中期/第四章/数据/VICON/vicon_raw/healthy/1/Jiang yunying Cal 05_vicon.csv'
output_dir = 'D:/论文/中期/第四章/数据/VICON/test_output/'

print(f"Testing with file: {test_file}")

# 创建输出目录
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

try:
    # 读取数据
    vicon_data = pd.read_csv(test_file)
    print(f"Data shape: {vicon_data.shape}")
    print(f"Columns: {list(vicon_data.columns)}")
    
    # 检查必要的列
    required_cols = ['RTHI.X', 'RTHI.Y', 'RTHI.Z', 'RKNE.X', 'RKNE.Y', 'RKNE.Z',
                   'RTIB.X', 'RTIB.Y', 'RTIB.Z', 'LTHI.X', 'LTHI.Y', 'LTHI.Z',
                   'LKNE.X', 'LKNE.Y', 'LKNE.Z', 'LTIB.X', 'LTIB.Y', 'LTIB.Z',
                   'RANK.X', 'RANK.Y', 'RANK.Z', 'RTOE.X', 'RTOE.Y', 'RTOE.Z',
                   'LANK.X', 'LANK.Y', 'LANK.Z', 'LTOE.X', 'LTOE.Y', 'LTOE.Z']
    
    missing_cols = [col for col in required_cols if col not in vicon_data.columns]
    if missing_cols:
        print(f"Missing columns: {missing_cols}")
    else:
        print("All required columns present!")
    
    # 计算关节角度
    fs = 100  # 采样率
    
    print("Calculating knee angles...")
    RKNEE_angle, LKNEE_angle = get_knee_angle(vicon_data)
    print(f"Knee angles calculated. Right knee: {len(RKNEE_angle)}, Left knee: {len(LKNEE_angle)}")
    
    print("Calculating ankle angles...")
    RANK_angle, LANK_angle = get_ankle_angle(vicon_data)
    print(f"Ankle angles calculated. Right ankle: {len(RANK_angle)}, Left ankle: {len(LANK_angle)}")
    
    # 滤波
    print("Applying filters...")
    RKNEE_angle_filtered = butterworth_zero_phase(RKNEE_angle, fs, cutoff=6, order=4)
    LKNEE_angle_filtered = butterworth_zero_phase(LKNEE_angle, fs, cutoff=6, order=4)
    RANK_angle_filtered = butterworth_zero_phase(RANK_angle, fs, cutoff=6, order=4)
    LANK_angle_filtered = butterworth_zero_phase(LANK_angle, fs, cutoff=6, order=4)
    
    # 计算角速度
    print("Calculating angular velocities...")
    RKNEE_speed = get_joint_angle_speed(RKNEE_angle_filtered)
    LKNEE_speed = get_joint_angle_speed(LKNEE_angle_filtered)
    RANK_speed = get_joint_angle_speed(RANK_angle_filtered)
    LANK_speed = get_joint_angle_speed(LANK_angle_filtered)
    
    # 计算角加速度
    print("Calculating angular accelerations...")
    RKNEE_acc = get_joint_angle_speed(RKNEE_speed)
    LKNEE_acc = get_joint_angle_speed(LKNEE_speed)
    RANK_acc = get_joint_angle_speed(RANK_speed)
    LANK_acc = get_joint_angle_speed(LANK_speed)
    
    # 准备输出数据
    print("Preparing output data...")
    
    # 去除空值
    vicon_data_clean = vicon_data.dropna(subset=['Frame', 'Sub Frame']).reset_index(drop=True)
    
    # 创建结果字典
    result_dict = {
        'Frame': vicon_data_clean['Frame'],
        'Sub Frame': vicon_data_clean['Sub Frame'],
        'Group': ['healthy'] * len(vicon_data_clean),
        'Subject': ['1'] * len(vicon_data_clean),
        'File_ID': ['Jiang yunying Cal 05'] * len(vicon_data_clean),
        'RKNEE_angle': RKNEE_angle_filtered,
        'LKNEE_angle': LKNEE_angle_filtered,
        'RANK_angle': RANK_angle_filtered,
        'LANK_angle': LANK_angle_filtered,
    }
    
    # 补齐长度（角速度和角加速度比角度少1和2个点）
    max_len = len(vicon_data_clean)
    
    # 角速度（少1个点）
    RKNEE_speed_padded = np.concatenate([RKNEE_speed, [np.nan]])
    LKNEE_speed_padded = np.concatenate([LKNEE_speed, [np.nan]])
    RANK_speed_padded = np.concatenate([RANK_speed, [np.nan]])
    LANK_speed_padded = np.concatenate([LANK_speed, [np.nan]])
    
    # 角加速度（少2个点）
    RKNEE_acc_padded = np.concatenate([RKNEE_acc, [np.nan, np.nan]])
    LKNEE_acc_padded = np.concatenate([LKNEE_acc, [np.nan, np.nan]])
    RANK_acc_padded = np.concatenate([RANK_acc, [np.nan, np.nan]])
    LANK_acc_padded = np.concatenate([LANK_acc, [np.nan, np.nan]])
    
    result_dict.update({
        'RKNEE_angle_speed': RKNEE_speed_padded,
        'LKNEE_angle_speed': LKNEE_speed_padded,
        'RANK_angle_speed': RANK_speed_padded,
        'LANK_angle_speed': LANK_speed_padded,
        'RKNEE_angle_acc': RKNEE_acc_padded,
        'LKNEE_angle_acc': LKNEE_acc_padded,
        'RANK_angle_acc': RANK_acc_padded,
        'LANK_angle_acc': LANK_acc_padded,
    })
    
    # 保存结果
    output_file = os.path.join(output_dir, 'test_joint_angles.csv')
    result_df = pd.DataFrame(result_dict)
    result_df.to_csv(output_file, index=False)
    
    print(f"Test completed successfully!")
    print(f"Output saved to: {output_file}")
    print(f"Output shape: {result_df.shape}")
    print(f"Sample data:")
    print(result_df.head())
    
    # 显示一些统计信息
    print(f"\nStatistics:")
    print(f"Right knee angle range: {np.nanmin(RKNEE_angle_filtered):.3f} to {np.nanmax(RKNEE_angle_filtered):.3f}")
    print(f"Left knee angle range: {np.nanmin(LKNEE_angle_filtered):.3f} to {np.nanmax(LKNEE_angle_filtered):.3f}")
    print(f"Right ankle angle range: {np.nanmin(RANK_angle_filtered):.3f} to {np.nanmax(RANK_angle_filtered):.3f}")
    print(f"Left ankle angle range: {np.nanmin(LANK_angle_filtered):.3f} to {np.nanmax(LANK_angle_filtered):.3f}")
    
except Exception as e:
    print(f"Error: {str(e)}")
    import traceback
    traceback.print_exc()
