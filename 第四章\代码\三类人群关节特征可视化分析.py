import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from scipy import stats
from matplotlib import rcParams
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def load_data():
    """加载统计数据"""
    base_path = r"D:\论文\中期\第四章\数据\VICON\joint_statistics"

    # 加载完整统计数据
    stats_file = os.path.join(base_path, "all_joint_trial_statistics.csv")
    if not os.path.exists(stats_file):
        print(f"数据文件不存在: {stats_file}")
        return None, None

    stats_df = pd.read_csv(stats_file)

    # 加载汇总统计数据
    summary_file = os.path.join(base_path, "group_feature_summary.csv")
    summary_df = pd.read_csv(summary_file) if os.path.exists(summary_file) else None

    return stats_df, summary_df

def calculate_cohens_d(feature, comparison, stats_df):
    """计算Cohen's d效应量"""
    if comparison == '健康_vs_早期PD':
        group1 = stats_df[stats_df['group_label'] == 0][feature].dropna()
        group2 = stats_df[stats_df['group_label'] == 1][feature].dropna()
    elif comparison == '健康_vs_中期PD':
        group1 = stats_df[stats_df['group_label'] == 0][feature].dropna()
        group2 = stats_df[stats_df['group_label'] == 2][feature].dropna()
    elif comparison == '早期PD_vs_中期PD':
        group1 = stats_df[stats_df['group_label'] == 1][feature].dropna()
        group2 = stats_df[stats_df['group_label'] == 2][feature].dropna()
    else:
        return np.nan

    if len(group1) == 0 or len(group2) == 0:
        return np.nan

    # 计算Cohen's d
    pooled_std = np.sqrt(((len(group1) - 1) * np.var(group1, ddof=1) +
                         (len(group2) - 1) * np.var(group2, ddof=1)) /
                        (len(group1) + len(group2) - 2))

    if pooled_std == 0:
        return np.nan

    cohens_d = (np.mean(group1) - np.mean(group2)) / pooled_std
    return abs(cohens_d)

def create_boxplot_comparison(stats_df, output_path):
    """创建三组箱线图对比"""
    rcParams['font.sans-serif'] = ['SimHei']
    # 获取原始特征列（均值）
    angle_features = ['RKNEE_angle_mean', 'LKNEE_angle_mean', 'RANK_angle_mean', 'LANK_angle_mean']
    speed_features = ['RKNEE_angle_speed_mean', 'LKNEE_angle_speed_mean', 'RANK_angle_speed_mean', 'LANK_angle_speed_mean']
    acc_features = ['RKNEE_angle_acc_mean', 'LKNEE_angle_acc_mean', 'RANK_angle_acc_mean', 'LANK_angle_acc_mean']
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle('三类人群关节特征箱线图对比', fontsize=16, fontweight='bold')
    
    # 角度特征
    for i, feature in enumerate(angle_features):
        ax = axes[0, i]
        if feature in stats_df.columns:
            sns.boxplot(x='group_label', y=feature, data=stats_df, ax=ax)
            ax.set_title(f'{feature.replace("_mean", "")}')
            ax.set_xlabel('组别 (0=健康, 1=早期PD, 2=中期PD)')
            ax.set_ylabel('角度 (弧度)')
    
    # 速度特征
    for i, feature in enumerate(speed_features):
        ax = axes[1, i]
        if feature in stats_df.columns:
            sns.boxplot(x='group_label', y=feature, data=stats_df, ax=ax)
            ax.set_title(f'{feature.replace("_mean", "")}')
            ax.set_xlabel('组别 (0=健康, 1=早期PD, 2=中期PD)')
            ax.set_ylabel('角速度 (弧度/秒)')
    
    # 加速度特征
    for i, feature in enumerate(acc_features):
        ax = axes[2, i]
        if feature in stats_df.columns:
            sns.boxplot(x='group_label', y=feature, data=stats_df, ax=ax)
            ax.set_title(f'{feature.replace("_mean", "")}')
            ax.set_xlabel('组别 (0=健康, 1=早期PD, 2=中期PD)')
            ax.set_ylabel('角加速度 (弧度/秒²)')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, "three_group_boxplot_comparison.png"), 
                dpi=300, bbox_inches='tight')
    plt.close()

def create_violin_plot(stats_df, output_path):
    """创建小提琴图"""
    rcParams['font.sans-serif'] = ['SimHei']
    # 选择主要特征
    main_features = ['RKNEE_angle_mean', 'LKNEE_angle_mean', 'RANK_angle_mean', 'LANK_angle_mean']
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    axes = axes.flatten()
    
    for i, feature in enumerate(main_features):
        if feature in stats_df.columns:
            sns.violinplot(x='group_label', y=feature, data=stats_df, ax=axes[i])
            axes[i].set_title(f'{feature.replace("_mean", "")} 分布对比')
            axes[i].set_xlabel('组别 (0=健康, 1=早期PD, 2=中期PD)')
            axes[i].set_ylabel('角度 (弧度)')
    
    plt.suptitle('三类人群关节角度分布小提琴图', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, "three_group_violin_plot.png"), 
                dpi=300, bbox_inches='tight')
    plt.close()

def create_range_comparison(stats_df, output_path):
    """创建变化范围对比图"""
    rcParams['font.sans-serif'] = ['SimHei']
    range_features = ['RKNEE_angle_range', 'LKNEE_angle_range', 'RANK_angle_range', 'LANK_angle_range']
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    axes = axes.flatten()

    for ax in axes:
        ax.grid(False)   
    
    for i, feature in enumerate(range_features):
        if feature in stats_df.columns:
            # 计算组间统计
            group_stats = stats_df.groupby('group_label')[feature].agg(['mean', 'std', 'min', 'max'])
            
            # 绘制条形图
            x = [0, 1, 2]
            means = group_stats['mean'].values
            stds = group_stats['std'].values
            
            axes[i].bar(x, means, yerr=stds, capsize=5, alpha=0.7, 
                       color=['skyblue', , 'lightgreen''lightcoral'])
            axes[i].set_title(f'{feature.replace("_range", "")} 变化范围对比')
            axes[i].set_xlabel('组别 (0=健康, 1=早期PD, 2=中期PD)')
            axes[i].set_ylabel('变化范围 (弧度)')
            axes[i].set_xticks(x)
            axes[i].set_xticklabels(['健康', '早期PD', '中期PD'])
            
            # 添加数值标签
            for j, (mean, std) in enumerate(zip(means, stds)):
                axes[i].text(j, mean + std + 0.01, f'{mean:.3f}±{std:.3f}', 
                           ha='center', va='bottom', fontsize=8)
    
    plt.suptitle('三类人群关节角度变化范围对比', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, "three_group_range_comparison.png"), 
                dpi=300, bbox_inches='tight')
    plt.close()

def create_heatmap(summary_df, output_path):
    """创建特征热图"""
    rcParams['font.sans-serif'] = ['SimHei']
    if summary_df is None:
        return
    
    # 准备热图数据
    features = summary_df['feature'].unique()
    angle_features = [f for f in features if 'angle_mean' in f]
    
    # 创建数据矩阵
    heatmap_data = []
    feature_names = []
    
    for feature in angle_features:
        feature_data = summary_df[summary_df['feature'] == feature]
        if len(feature_data) == 3:  # 确保三个组都有数据
            healthy = feature_data[feature_data['group_label'] == 0]['mean'].iloc[0]
            pd_early = feature_data[feature_data['group_label'] == 1]['mean'].iloc[0]
            pd_mid = feature_data[feature_data['group_label'] == 2]['mean'].iloc[0]
            heatmap_data.append([healthy, pd_early, pd_mid])
            feature_names.append(feature.replace('_mean', ''))
    
    if heatmap_data:
        heatmap_df = pd.DataFrame(heatmap_data, 
                                 index=feature_names, 
                                 columns=['健康人群', '早期PD', '中期PD'])
        
        plt.figure(figsize=(10, 8))
        sns.heatmap(heatmap_df, annot=True, cmap='RdYlBu_r', center=0, 
                   fmt='.3f', cbar_kws={'label': '角度均值 (弧度)'})
        plt.title('三类人群关节角度均值热图')
        plt.ylabel('关节特征')
        plt.xlabel('人群组别')
        plt.tight_layout()
        plt.savefig(os.path.join(output_path, "three_group_heatmap.png"), 
                    dpi=300, bbox_inches='tight')
        plt.close()

def create_statistical_comparison(stats_df, output_path):
    """创建统计检验结果图"""
    rcParams['font.sans-serif'] = ['SimHei']
    # 选择主要特征进行方差分析
    main_features = ['RKNEE_angle_mean', 'LKNEE_angle_mean', 'RANK_angle_mean', 'LANK_angle_mean',
                    'RKNEE_angle_range', 'LKNEE_angle_range', 'RANK_angle_range', 'LANK_angle_range']

    test_results = []
    posthoc_results = []

    for feature in main_features:
        if feature in stats_df.columns:
            healthy = stats_df[stats_df['group_label'] == 0][feature].dropna()
            pd_early = stats_df[stats_df['group_label'] == 1][feature].dropna()
            pd_mid = stats_df[stats_df['group_label'] == 2][feature].dropna()

            if len(healthy) > 0 and len(pd_early) > 0 and len(pd_mid) > 0:
                # 进行方差分析
                f_stat, p_value = stats.f_oneway(healthy, pd_early, pd_mid)

                # 计算效应量 (eta squared)
                total_mean = np.mean(np.concatenate([healthy, pd_early, pd_mid]))
                ss_between = len(healthy) * (np.mean(healthy) - total_mean)**2 + \
                           len(pd_early) * (np.mean(pd_early) - total_mean)**2 + \
                           len(pd_mid) * (np.mean(pd_mid) - total_mean)**2
                ss_total = np.sum((np.concatenate([healthy, pd_early, pd_mid]) - total_mean)**2)
                eta_squared = ss_between / ss_total if ss_total > 0 else 0

                # 事后检验 - Tukey HSD
                posthoc_pvalues = {}
                posthoc_significant = {}

                # 健康 vs 早期PD
                _, p_val_he = stats.ttest_ind(healthy, pd_early)
                # Bonferroni校正 (3个比较，所以alpha = 0.05/3)
                p_val_he_corrected = min(p_val_he * 3, 1.0)
                posthoc_pvalues['健康_vs_早期PD'] = p_val_he_corrected
                posthoc_significant['健康_vs_早期PD'] = p_val_he_corrected < 0.05

                # 健康 vs 中期PD
                _, p_val_hm = stats.ttest_ind(healthy, pd_mid)
                p_val_hm_corrected = min(p_val_hm * 3, 1.0)
                posthoc_pvalues['健康_vs_中期PD'] = p_val_hm_corrected
                posthoc_significant['健康_vs_中期PD'] = p_val_hm_corrected < 0.05

                # 早期PD vs 中期PD
                _, p_val_em = stats.ttest_ind(pd_early, pd_mid)
                p_val_em_corrected = min(p_val_em * 3, 1.0)
                posthoc_pvalues['早期PD_vs_中期PD'] = p_val_em_corrected
                posthoc_significant['早期PD_vs_中期PD'] = p_val_em_corrected < 0.05

                test_results.append({
                    'feature': feature.replace('_mean', '').replace('_range', '_范围'),
                    'f_statistic': f_stat,
                    'p_value': p_value,
                    'significant': p_value < 0.05,
                    'eta_squared': eta_squared,
                    'healthy_mean': np.mean(healthy),
                    'pd_early_mean': np.mean(pd_early),
                    'pd_mid_mean': np.mean(pd_mid),
                    'healthy_std': np.std(healthy),
                    'pd_early_std': np.std(pd_early),
                    'pd_mid_std': np.std(pd_mid)
                })

                # 保存事后检验结果
                for comparison, p_val in posthoc_pvalues.items():
                    posthoc_results.append({
                        'feature': feature.replace('_mean', '').replace('_range', '_范围'),
                        'comparison': comparison,
                        'p_value': p_val,
                        'significant': posthoc_significant[comparison],
                        'effect_size': calculate_cohens_d(feature, comparison, stats_df)
                    })

    if test_results:
        results_df = pd.DataFrame(test_results)
        posthoc_df = pd.DataFrame(posthoc_results)

        # 创建三个子图：ANOVA结果、事后检验结果、效应量对比
        fig, (ax1, ax2, ax3) = plt.subplots(1, 3, figsize=(24, 8))

        # 1. ANOVA p值条形图
        colors = ['red' if sig else 'gray' for sig in results_df['significant']]
        bars = ax1.bar(range(len(results_df)), -np.log10(results_df['p_value']), color=colors)
        ax1.axhline(y=-np.log10(0.05), color='red', linestyle='--', label='p=0.05')
        ax1.set_xlabel('特征')
        ax1.set_ylabel('-log10(p值)')
        ax1.set_title('方差分析(ANOVA)显著性检验结果')
        ax1.set_xticks(range(len(results_df)))
        ax1.set_xticklabels(results_df['feature'], rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 事后检验热图
        # 创建事后检验矩阵
        features = posthoc_df['feature'].unique()
        comparisons = posthoc_df['comparison'].unique()

        posthoc_matrix = np.zeros((len(features), len(comparisons)))
        for i, feature in enumerate(features):
            for j, comparison in enumerate(comparisons):
                mask = (posthoc_df['feature'] == feature) & (posthoc_df['comparison'] == comparison)
                if mask.any():
                    p_val = posthoc_df[mask]['p_value'].iloc[0]
                    posthoc_matrix[i, j] = -np.log10(p_val)

        im = ax2.imshow(posthoc_matrix, cmap='Reds', aspect='auto')
        ax2.set_xticks(range(len(comparisons)))
        ax2.set_xticklabels(comparisons, rotation=45, ha='right')
        ax2.set_yticks(range(len(features)))
        ax2.set_yticklabels(features)
        ax2.set_title('事后检验显著性热图\n(-log10(p值))')

        # 添加数值标注
        for i in range(len(features)):
            for j in range(len(comparisons)):
                text = ax2.text(j, i, f'{posthoc_matrix[i, j]:.2f}',
                               ha="center", va="center", color="black", fontsize=8)

        # 添加颜色条
        plt.colorbar(im, ax=ax2, label='-log10(p值)')

        # 3. 效应量对比
        ax3.bar(range(len(results_df)), results_df['eta_squared'], color='orange', alpha=0.7)
        ax3.set_xlabel('特征')
        ax3.set_ylabel('效应量 (η²)')
        ax3.set_title('组间差异效应量')
        ax3.set_xticks(range(len(results_df)))
        ax3.set_xticklabels(results_df['feature'], rotation=45, ha='right')
        ax3.grid(True, alpha=0.3)

        # 添加效应量解释线
        ax3.axhline(y=0.01, color='green', linestyle='--', alpha=0.7, label='小效应(0.01)')
        ax3.axhline(y=0.06, color='orange', linestyle='--', alpha=0.7, label='中等效应(0.06)')
        ax3.axhline(y=0.14, color='red', linestyle='--', alpha=0.7, label='大效应(0.14)')
        ax3.legend()

        plt.tight_layout()
        plt.savefig(os.path.join(output_path, "three_group_statistical_comparison.png"),
                    dpi=300, bbox_inches='tight')
        plt.close()

        # 保存统计结果
        results_df.to_csv(os.path.join(output_path, "three_group_anova_results.csv"),
                         index=False, encoding='utf-8-sig')
        posthoc_df.to_csv(os.path.join(output_path, "three_group_posthoc_results.csv"),
                         index=False, encoding='utf-8-sig')

        # 生成详细的事后检验报告
        generate_posthoc_report(results_df, posthoc_df, output_path)

def generate_posthoc_report(results_df, posthoc_df, output_path):
    """生成详细的事后检验报告"""
    report_lines = []
    report_lines.append("=== 三类人群关节特征统计分析报告 ===\n")
    report_lines.append("分析说明:")
    report_lines.append("- 使用单因素方差分析(ANOVA)检验三组间总体差异")
    report_lines.append("- 使用Bonferroni校正的t检验进行事后检验")
    report_lines.append("- 效应量使用η²(eta squared)和Cohen's d表示\n")

    report_lines.append("效应量解释:")
    report_lines.append("- η² < 0.01: 小效应")
    report_lines.append("- 0.01 ≤ η² < 0.06: 小到中等效应")
    report_lines.append("- 0.06 ≤ η² < 0.14: 中等效应")
    report_lines.append("- η² ≥ 0.14: 大效应")
    report_lines.append("- Cohen's d < 0.2: 小效应, 0.2-0.8: 中等效应, >0.8: 大效应\n")

    # ANOVA结果汇总
    report_lines.append("=== 方差分析(ANOVA)结果汇总 ===")
    significant_features = results_df[results_df['significant']]
    if len(significant_features) > 0:
        report_lines.append(f"发现 {len(significant_features)} 个特征在三组间存在显著差异 (p < 0.05):")
        for _, row in significant_features.iterrows():
            effect_level = "大效应" if row['eta_squared'] >= 0.14 else ("中等效应" if row['eta_squared'] >= 0.06 else "小效应")
            report_lines.append(f"- {row['feature']}: F={row['f_statistic']:.3f}, p={row['p_value']:.4f}, η²={row['eta_squared']:.3f} ({effect_level})")
    else:
        report_lines.append("未发现显著差异的特征")

    report_lines.append("")

    # 事后检验详细结果
    report_lines.append("=== 事后检验详细结果 ===")

    for feature in results_df['feature'].unique():
        feature_anova = results_df[results_df['feature'] == feature].iloc[0]
        feature_posthoc = posthoc_df[posthoc_df['feature'] == feature]

        report_lines.append(f"\n【{feature}】")
        report_lines.append(f"ANOVA: F={feature_anova['f_statistic']:.3f}, p={feature_anova['p_value']:.4f}")
        report_lines.append(f"各组均值: 健康={feature_anova['healthy_mean']:.4f}±{feature_anova['healthy_std']:.4f}, " +
                          f"早期PD={feature_anova['pd_early_mean']:.4f}±{feature_anova['pd_early_std']:.4f}, " +
                          f"中期PD={feature_anova['pd_mid_mean']:.4f}±{feature_anova['pd_mid_std']:.4f}")

        if feature_anova['significant']:
            report_lines.append("事后检验结果:")
            for _, posthoc_row in feature_posthoc.iterrows():
                significance = "***" if posthoc_row['p_value'] < 0.001 else ("**" if posthoc_row['p_value'] < 0.01 else ("*" if posthoc_row['p_value'] < 0.05 else "ns"))
                effect_size_level = "大" if posthoc_row['effect_size'] > 0.8 else ("中" if posthoc_row['effect_size'] > 0.2 else "小")
                report_lines.append(f"  {posthoc_row['comparison']}: p={posthoc_row['p_value']:.4f} {significance}, Cohen's d={posthoc_row['effect_size']:.3f} ({effect_size_level}效应)")
        else:
            report_lines.append("无显著差异，未进行事后检验")

    # 保存报告
    report_file = os.path.join(output_path, "detailed_statistical_report.txt")
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))

    print(f"详细统计报告已保存: {report_file}")

def create_comprehensive_dashboard(stats_df, summary_df, output_path):
    """创建综合仪表板"""
    rcParams['font.sans-serif'] = ['SimHei']
    fig = plt.figure(figsize=(20, 16))
    
    # 1. 主要角度特征对比
    ax1 = plt.subplot(3, 3, 1)
    main_angles = ['RKNEE_angle_mean', 'LKNEE_angle_mean', 'RANK_angle_mean', 'LANK_angle_mean']
    group_means = {0: [], 1: [], 2: []}
    
    for feature in main_angles:
        if feature in stats_df.columns:
            for group_label in [0, 1, 2]:
                group_mean = stats_df[stats_df['group_label'] == group_label][feature].mean()
                group_means[group_label].append(group_mean)
    
    x = np.arange(len(main_angles))
    width = 0.25
    ax1.bar(x - width, group_means[0], width, label='健康人群', alpha=0.8)
    ax1.bar(x, group_means[1], width, label='早期PD', alpha=0.8)
    ax1.bar(x + width, group_means[2], width, label='中期PD', alpha=0.8)
    ax1.set_xlabel('关节')
    ax1.set_ylabel('平均角度 (弧度)')
    ax1.set_title('主要关节角度对比')
    ax1.set_xticks(x)
    ax1.set_xticklabels([f.replace('_angle_mean', '') for f in main_angles], rotation=45)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 变化范围对比
    ax2 = plt.subplot(3, 3, 2)
    range_features = ['RKNEE_angle_range', 'LKNEE_angle_range', 'RANK_angle_range', 'LANK_angle_range']
    range_means = {0: [], 1: [], 2: []}
    
    for feature in range_features:
        if feature in stats_df.columns:
            for group_label in [0, 1, 2]:
                group_mean = stats_df[stats_df['group_label'] == group_label][feature].mean()
                range_means[group_label].append(group_mean)
    
    x = np.arange(len(range_features))
    ax2.bar(x - width, range_means[0], width, label='健康人群', alpha=0.8)
    ax2.bar(x, range_means[1], width, label='早期PD', alpha=0.8)
    ax2.bar(x + width, range_means[2], width, label='中期PD', alpha=0.8)
    ax2.set_xlabel('关节')
    ax2.set_ylabel('变化范围 (弧度)')
    ax2.set_title('关节活动范围对比')
    ax2.set_xticks(x)
    ax2.set_xticklabels([f.replace('_angle_range', '') for f in range_features], rotation=45)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3-6. 各关节的详细分布
    joints = ['RKNEE', 'LKNEE', 'RANK', 'LANK']
    for i, joint in enumerate(joints):
        ax = plt.subplot(3, 3, i+3)
        feature = f'{joint}_angle_mean'
        if feature in stats_df.columns:
            sns.boxplot(x='group_label', y=feature, data=stats_df, ax=ax)
            ax.set_title(f'{joint} 角度分布')
            ax.set_xlabel('组别')
            ax.set_ylabel('角度 (弧度)')
            ax.set_xticklabels(['健康', '早期PD', '中期PD'])
    
    # 7. 样本分布
    ax7 = plt.subplot(3, 3, 7)
    group_counts = stats_df['group_label'].value_counts().sort_index()
    labels = ['健康人群', '早期PD', '中期PD']
    ax7.pie(group_counts.values, labels=labels, autopct='%1.1f%%')
    ax7.set_title('样本分布')
    
    # 8. 受试者数量统计
    ax8 = plt.subplot(3, 3, 8)
    subject_counts = stats_df.groupby('group_label')['subject_id'].nunique()
    ax8.bar(['健康', '早期PD', '中期PD'], subject_counts.values, 
            color=['skyblue', 'lightcoral', 'lightgreen'])
    ax8.set_title('受试者数量')
    ax8.set_ylabel('受试者数')
    
    plt.suptitle('三类人群关节特征差异分析综合仪表板', fontsize=20, fontweight='bold')
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, "three_group_comprehensive_dashboard.png"), 
                dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    # 加载数据
    stats_df, summary_df = load_data()
    
    if stats_df is None:
        print("无法加载数据，请先运行特征统计脚本")
        return
    
    # 创建输出目录
    output_path = r"D:\论文\中期\第四章\数据\VICON\joint_visualization"
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    
    print("开始生成三类人群关节特征可视化图表...")
    print(f"数据样本数: {len(stats_df)}")
    print(f"健康人群: {len(stats_df[stats_df['group_label'] == 0])}")
    print(f"早期PD: {len(stats_df[stats_df['group_label'] == 1])}")
    print(f"中期PD: {len(stats_df[stats_df['group_label'] == 2])}")
    
    # 生成各种图表
    print("\n1. 生成三组箱线图对比...")
    create_boxplot_comparison(stats_df, output_path)
    
    print("2. 生成小提琴图...")
    create_violin_plot(stats_df, output_path)
    
    print("3. 生成变化范围对比图...")
    create_range_comparison(stats_df, output_path)
    
    print("4. 生成特征热图...")
    create_heatmap(summary_df, output_path)
    
    print("5. 生成统计检验结果图...")
    create_statistical_comparison(stats_df, output_path)
    
    print("6. 生成综合仪表板...")
    create_comprehensive_dashboard(stats_df, summary_df, output_path)
    
    print(f"\n所有可视化图表已保存到: {output_path}")
    print("生成的图表包括:")
    print("- three_group_boxplot_comparison.png: 三组箱线图对比")
    print("- three_group_violin_plot.png: 小提琴图")
    print("- three_group_range_comparison.png: 变化范围对比")
    print("- three_group_heatmap.png: 特征热图")
    print("- three_group_statistical_comparison.png: 统计检验结果(含事后检验)")
    print("- three_group_comprehensive_dashboard.png: 综合仪表板")
    print("\n生成的统计分析文件包括:")
    print("- three_group_anova_results.csv: 方差分析结果")
    print("- three_group_posthoc_results.csv: 事后检验结果")
    print("- detailed_statistical_report.txt: 详细统计分析报告")
    print("\n事后检验说明:")
    print("- 使用Bonferroni校正控制多重比较的I类错误")
    print("- 比较组合: 健康vs早期PD, 健康vs中期PD, 早期PD vs中期PD")
    print("- 效应量使用Cohen's d衡量实际差异大小")

if __name__ == "__main__":
    main()
